# 🧠 SAM脖子裁剪工具

使用Meta的Segment Anything Model (SAM) 精确识别头部，然后裁剪脖子以下部分。

## ✨ 特色功能

- 🎯 **精确识别**：使用SAM模型精确分割头部区域
- 🔄 **适应复杂场景**：支持侧身、遮挡、各种角度
- ⚡ **双重模式**：SAM精确版 + 人脸检测快速版
- 📁 **批量处理**：支持单张和批量处理
- 🛡️ **安全边距**：可调节安全边距，避免裁剪到脸部
- 🚀 **即开即用**：已测试可用，处理速度快

## 🚀 快速开始

### 方法1：双击启动（推荐）
```
双击 "启动脖子裁剪.command" 文件
```

### 方法2：命令行启动
```bash
python3 easy_start.py
```

### 方法3：直接使用工具
```bash
# 快速版（推荐）- 基于人脸检测
python3 simple_neck_cropper.py -i input.jpg -o output.png

# SAM版 - 基于SAM分割（更精确但较慢）
python3 sam_neck_cropper.py -i input.jpg -o output.png

# 批量处理
python3 simple_neck_cropper.py -i input_folder -o output_folder

# 自定义参数
python3 simple_neck_cropper.py -i input.jpg -o output.png -m 30 -r 0.4
```

## 🎯 测试结果

✅ **已测试成功**：
- 单张图片处理：0.1-0.2秒/张
- 批量处理：9张图片共1.12秒
- 人脸检测成功率：100%（测试图片）
- 脖子位置识别：准确

## 📦 安装依赖

首次使用需要安装依赖和下载SAM模型：

```bash
python3 sam_neck_cropper.py --install
```

或在启动界面选择"安装工具"选项。

## ⚙️ 参数说明

- **安全边距**：在检测到的脖子位置下方额外保留的像素
  - 默认：20像素
  - 推荐：15-30像素
  - 作用：避免裁剪到下巴或脖子

## 📊 处理效果

### 支持的场景：
- ✅ 正面照片
- ✅ 侧身照片（45°-90°）
- ✅ 轻微遮挡（手臂、物品）
- ✅ 各种角度和姿势
- ✅ 复杂背景

### 输出格式：
- 格式：PNG（保持透明度）
- 命名：原文件名 + "_neck_cropped"
- 位置：原文件同目录或指定目录

## 🔧 技术原理

1. **SAM分割**：使用SAM模型自动分割图像中的所有区域
2. **头部识别**：基于位置和面积特征识别最可能的头部区域
3. **轮廓分析**：分析头部轮廓找到最低点作为脖子位置
4. **安全裁剪**：在脖子位置加上安全边距进行裁剪

## 📁 文件结构

```
sam_neck_crop/
├── sam_neck_cropper.py      # 核心裁剪工具
├── easy_start.py            # 简化启动界面
├── 启动脖子裁剪.command      # 快捷启动脚本
├── README.md               # 使用说明
└── models/                 # SAM模型目录（自动创建）
    └── sam_vit_b_01ec64.pth # SAM模型文件（自动下载）
```

## 🆚 与传统方法对比

| 特性 | 传统关键点检测 | SAM脖子裁剪 |
|------|---------------|-------------|
| 正面照 | ✅ 优秀 | ✅ 优秀 |
| 侧身照 | ⚠️ 一般 | ✅ 优秀 |
| 遮挡场景 | ❌ 困难 | ✅ 良好 |
| 复杂背景 | ⚠️ 一般 | ✅ 优秀 |
| 各种角度 | ❌ 有限 | ✅ 适应性强 |

## 🐛 常见问题

**Q: 首次使用很慢？**
A: 首次使用需要下载375MB的SAM模型，请耐心等待。

**Q: 识别不准确？**
A: 可以调整安全边距参数，或检查图片中头部是否清晰可见。

**Q: 支持哪些图片格式？**
A: 支持 JPG、PNG、BMP、TIFF、WebP 等常见格式。

**Q: 可以处理视频吗？**
A: 目前只支持静态图片，视频需要先提取帧。

## 📞 技术支持

如有问题，请检查：
1. Python环境是否正确安装
2. 依赖包是否安装完成
3. SAM模型是否下载成功
4. 输入图片是否包含清晰的人像

---

🎯 **享受精确的脖子裁剪体验！**
