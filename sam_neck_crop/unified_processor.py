#!/usr/bin/env python3
"""
统一图像处理工具
流程：RMBG-1.4抠图 → Haar Cascades裁剪 → SAM-B备用裁剪 → 失败分类
"""

import os
import cv2
import numpy as np
from PIL import Image
import argparse
from pathlib import Path
import time
import shutil
from datetime import datetime
import sys

# 添加抠图工具路径
sys.path.append('../auto_matting_tool')

def create_output_structure(output_dir):
    """创建输出文件夹结构"""
    success_dir = os.path.join(output_dir, "success")
    failed_dir = os.path.join(output_dir, "failed")
    
    os.makedirs(success_dir, exist_ok=True)
    os.makedirs(failed_dir, exist_ok=True)
    
    return success_dir, failed_dir

def get_image_files(input_path):
    """获取所有图片文件"""
    supported_formats = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.webp'}
    
    if os.path.isfile(input_path):
        return [input_path]
    
    image_files = []
    for root, dirs, files in os.walk(input_path):
        for file in files:
            if Path(file).suffix.lower() in supported_formats:
                image_files.append(os.path.join(root, file))
    
    return sorted(image_files)

def rmbg_matting(image_path, temp_dir):
    """使用RMBG-1.4进行抠图"""
    try:
        from rembg import remove, new_session
        
        print(f"📸 RMBG-1.4抠图: {os.path.basename(image_path)}")
        
        # 创建会话
        session = new_session('rmbg-1.4')
        
        # 读取原图
        with open(image_path, 'rb') as f:
            input_data = f.read()
        
        # 抠图
        output_data = remove(input_data, session=session)
        
        # 保存临时文件
        temp_filename = f"temp_{int(time.time())}_{os.path.basename(image_path)}.png"
        temp_path = os.path.join(temp_dir, temp_filename)
        
        with open(temp_path, 'wb') as f:
            f.write(output_data)
        
        print(f"✅ 抠图完成: {temp_path}")
        return temp_path
        
    except Exception as e:
        print(f"❌ RMBG-1.4抠图失败: {e}")
        return None

def haar_crop_detection(image_path):
    """使用Haar Cascades检测人脸并计算脖子位置"""
    try:
        # 读取图像
        pil_image = Image.open(image_path).convert('RGBA')
        img_array = np.array(pil_image)
        
        # 转换为RGB用于检测
        rgb_array = img_array[:, :, :3]
        gray = cv2.cvtColor(rgb_array, cv2.COLOR_RGB2GRAY)
        
        # 正面人脸检测
        face_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_frontalface_default.xml')
        faces = face_cascade.detectMultiScale(gray, 1.1, 4)
        
        # 侧面人脸检测
        if len(faces) == 0:
            profile_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_profileface.xml')
            faces = profile_cascade.detectMultiScale(gray, 1.1, 4)
        
        if len(faces) == 0:
            return False, "未检测到人脸"
        
        # 找到最大的人脸
        largest_face = max(faces, key=lambda face: face[2] * face[3])
        x, y, w, h = largest_face
        
        # 检查人脸大小是否合理
        image_height, image_width = gray.shape
        face_area_ratio = (w * h) / (image_width * image_height)
        
        if face_area_ratio < 0.01:
            return False, "检测到的人脸过小"
        
        if face_area_ratio > 0.8:
            return False, "检测到的人脸过大"
        
        # 计算脖子位置
        neck_y = y + h + int(h * 0.3) + 20  # 脸高度30% + 20px安全边距
        
        # 确保不超出图像边界
        neck_y = min(neck_y, image_height - 10)
        
        print(f"✅ Haar检测成功: 人脸{w}x{h}, 脖子位置Y={neck_y}")
        return True, neck_y
        
    except Exception as e:
        return False, f"Haar检测出错: {e}"

def sam_crop_analysis(image_path):
    """使用SAM-B进行轮廓分析"""
    try:
        # 读取PNG图像
        pil_image = Image.open(image_path).convert('RGBA')
        img_array = np.array(pil_image)
        alpha_array = img_array[:, :, 3]
        
        # 检查是否有内容
        if np.sum(alpha_array > 0) == 0:
            return False, "图像完全透明"
        
        # 创建轮廓mask
        head_mask = (alpha_array > 128).astype(np.uint8) * 255
        contours, _ = cv2.findContours(head_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        if len(contours) == 0:
            return False, "无法找到有效轮廓"
        
        # 找到最大轮廓
        largest_contour = max(contours, key=cv2.contourArea)
        area = cv2.contourArea(largest_contour)
        
        if area < 1000:
            return False, "主体轮廓过小"
        
        # 找到轮廓最低点
        bottom_points = []
        for point in largest_contour:
            x, y = point[0]
            bottom_points.append((x, y))
        
        if not bottom_points:
            return False, "无法找到轮廓点"
        
        # 按Y坐标排序，取最低点
        bottom_points.sort(key=lambda p: p[1], reverse=True)
        neck_y = bottom_points[0][1] + 20  # 加安全边距
        
        # 检查位置是否合理
        height = img_array.shape[0]
        if neck_y > height * 0.95:
            return False, f"脖子位置过低 (Y={neck_y}/{height})"
        
        if neck_y < height * 0.1:
            return False, f"脖子位置过高 (Y={neck_y}/{height})"
        
        print(f"✅ SAM分析成功: 脖子位置Y={neck_y}")
        return True, neck_y
        
    except Exception as e:
        return False, f"SAM分析出错: {e}"

def crop_and_save(image_path, crop_y, output_path):
    """裁剪图像并保存"""
    try:
        # 读取图像
        pil_image = Image.open(image_path).convert('RGBA')
        img_array = np.array(pil_image)
        
        # 裁剪
        cropped_array = img_array[:crop_y, :, :]
        
        # 保存
        result_image = Image.fromarray(cropped_array, 'RGBA')
        result_image.save(output_path)
        
        print(f"✅ 裁剪保存: {output_path}")
        return True
        
    except Exception as e:
        print(f"❌ 裁剪保存失败: {e}")
        return False

def log_failure(image_path, reason, failed_dir):
    """记录失败原因"""
    log_file = os.path.join(failed_dir, "processing_log.txt")
    
    with open(log_file, "a", encoding="utf-8") as f:
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        filename = os.path.basename(image_path)
        f.write(f"[{timestamp}] {filename} - {reason}\n")

def get_output_filename(input_path, suffix="processed"):
    """生成输出文件名"""
    base_name = Path(input_path).stem
    return f"{base_name}_{suffix}.png"

def process_single_image(image_path, success_dir, failed_dir, temp_dir):
    """处理单张图片的完整流程"""
    print(f"\n🎯 开始处理: {os.path.basename(image_path)}")

    # 步骤1：RMBG-1.4抠图
    matted_path = rmbg_matting(image_path, temp_dir)
    if not matted_path:
        return False, "抠图失败"

    # 步骤2：尝试Haar Cascades裁剪
    print("⚡ 尝试Haar Cascades检测...")
    haar_success, haar_result = haar_crop_detection(matted_path)

    if haar_success:
        output_path = os.path.join(success_dir, get_output_filename(image_path, "haar_cropped"))
        if crop_and_save(matted_path, haar_result, output_path):
            # 清理临时文件
            os.remove(matted_path)
            return True, "Haar成功"

    # 步骤3：尝试SAM-B轮廓分析
    print("🧠 尝试SAM-B轮廓分析...")
    sam_success, sam_result = sam_crop_analysis(matted_path)

    if sam_success:
        output_path = os.path.join(success_dir, get_output_filename(image_path, "sam_cropped"))
        if crop_and_save(matted_path, sam_result, output_path):
            # 清理临时文件
            os.remove(matted_path)
            return True, "SAM成功"

    # 步骤4：所有方法都失败，保存到失败文件夹
    print("❌ 所有检测方法都失败")
    failed_path = os.path.join(failed_dir, get_output_filename(image_path, "matted_only"))

    # 移动抠图结果到失败文件夹
    shutil.move(matted_path, failed_path)

    # 记录失败原因
    failure_reason = f"Haar: {haar_result}, SAM: {sam_result}"
    log_failure(image_path, failure_reason, failed_dir)

    print(f"📁 已保存到失败文件夹: {failed_path}")
    return False, failure_reason

def batch_process(input_path, output_dir):
    """批量处理主函数"""
    print("🚀 统一图像处理工具")
    print("=" * 60)

    # 创建输出文件夹结构
    success_dir, failed_dir = create_output_structure(output_dir)

    # 创建临时文件夹
    temp_dir = os.path.join(output_dir, "temp")
    os.makedirs(temp_dir, exist_ok=True)

    # 获取所有图片文件
    image_files = get_image_files(input_path)

    if not image_files:
        print("❌ 未找到任何图片文件")
        return

    print(f"📁 找到 {len(image_files)} 张图片")
    print(f"📤 输出目录: {output_dir}")
    print("-" * 60)

    # 统计信息
    stats = {
        "total": len(image_files),
        "haar_success": 0,
        "sam_success": 0,
        "failed": 0,
        "failed_files": []
    }

    # 处理每张图片
    for i, image_file in enumerate(image_files, 1):
        print(f"\n[{i}/{len(image_files)}]", end=" ")

        success, result = process_single_image(image_file, success_dir, failed_dir, temp_dir)

        if success:
            if "haar" in result.lower():
                stats["haar_success"] += 1
            elif "sam" in result.lower():
                stats["sam_success"] += 1
        else:
            stats["failed"] += 1
            stats["failed_files"].append(os.path.basename(image_file))

    # 清理临时文件夹
    try:
        shutil.rmtree(temp_dir)
    except:
        pass

    # 打印统计结果
    print("\n" + "=" * 60)
    print("📊 处理完成统计:")
    print(f"总计: {stats['total']} 张")
    print(f"✅ Haar成功: {stats['haar_success']} 张")
    print(f"✅ SAM成功: {stats['sam_success']} 张")
    print(f"❌ 失败: {stats['failed']} 张")

    success_rate = (stats['haar_success'] + stats['sam_success']) / stats['total'] * 100
    print(f"🎯 成功率: {success_rate:.1f}%")

    if stats["failed"] > 0:
        print(f"\n❌ 失败文件列表:")
        for failed_file in stats["failed_files"]:
            print(f"  - {failed_file}")
        print(f"\n💡 失败的图片已保存到 failed/ 文件夹（仅抠图，未裁剪）")

    print(f"\n📁 结果保存在:")
    print(f"  成功: {success_dir}")
    print(f"  失败: {failed_dir}")

def main():
    parser = argparse.ArgumentParser(description='统一图像处理工具：抠图+智能裁剪')
    parser.add_argument('--input', '-i', required=True, help='输入图片文件或目录')
    parser.add_argument('--output', '-o', required=True, help='输出目录')

    args = parser.parse_args()

    if not os.path.exists(args.input):
        print(f"❌ 输入路径不存在: {args.input}")
        return

    # 开始批量处理
    batch_process(args.input, args.output)

if __name__ == '__main__':
    main()
