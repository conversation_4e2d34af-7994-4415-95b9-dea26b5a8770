#!/usr/bin/env python3
"""
简化版脖子裁剪工具
使用人脸检测 + 比例估算来快速裁剪脖子以下部分
适用于快速测试和简单场景
"""

import os
import cv2
import numpy as np
from PIL import Image
import argparse
from pathlib import Path
import time

def detect_face_and_estimate_neck(image_path, neck_ratio=0.3):
    """
    使用多种方法检测人脸并估算脖子位置

    Args:
        image_path: 输入图片路径
        neck_ratio: 脖子位置相对于人脸高度的比例

    Returns:
        neck_y: 脖子的Y坐标位置
        original_image: 原始图像
    """
    try:
        # 读取图像（保持透明通道）
        from PIL import Image as PILImage
        pil_image = PILImage.open(image_path)

        # 检查是否有透明通道
        has_alpha = pil_image.mode in ('RGBA', 'LA') or 'transparency' in pil_image.info
        print(f"🔍 图像模式: {pil_image.mode}, 有透明通道: {has_alpha}")

        if has_alpha:
            # 保持RGBA格式
            image_rgba = np.array(pil_image.convert('RGBA'))
            image_rgb = image_rgba[:, :, :3]  # RGB部分用于检测
            alpha_channel = image_rgba[:, :, 3]  # 保存透明通道
        else:
            # 没有透明通道，转换为RGB
            image_rgb = np.array(pil_image.convert('RGB'))
            alpha_channel = None

        # 转换为灰度图用于人脸检测
        gray = cv2.cvtColor(image_rgb, cv2.COLOR_RGB2GRAY)
        height, width = image_rgb.shape[:2]

        # 方法1：正面人脸检测
        face_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_frontalface_default.xml')
        faces = face_cascade.detectMultiScale(gray, 1.1, 4)

        # 方法2：侧面人脸检测
        profile_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_profileface.xml')
        profile_faces = profile_cascade.detectMultiScale(gray, 1.1, 4)

        # 合并检测结果
        all_faces = list(faces) + list(profile_faces)

        if len(all_faces) > 0:
            # 找到最大的人脸
            largest_face = max(all_faces, key=lambda face: face[2] * face[3])
            x, y, w, h = largest_face

            # 估算脖子位置：人脸底部 + 脸高度的一定比例
            neck_y = y + h + int(h * neck_ratio)

            print(f"✅ 检测到人脸，估算脖子位置: Y={neck_y}")
            return neck_y, image_rgb, alpha_channel

        # 方法3：使用肤色检测找到人脸区域
        print("⚠️ 未检测到人脸，尝试肤色检测...")
        neck_y = detect_skin_and_estimate_neck(image_rgb, neck_ratio)
        if neck_y is not None:
            print(f"✅ 通过肤色检测估算脖子位置: Y={neck_y}")
            return neck_y, image_rgb, alpha_channel

        # 方法4：智能比例估算
        print("⚠️ 使用智能比例估算...")
        # 对于人像照片，通常头部占图像上部的25-35%
        neck_y = int(height * 0.35)  # 更保守的估算

        print(f"✅ 智能比例估算脖子位置: Y={neck_y}")
        return neck_y, image_rgb, alpha_channel

    except Exception as e:
        print(f"❌ 人脸检测失败: {e}")
        return None, None

def detect_skin_and_estimate_neck(image_rgb, neck_ratio=0.3):
    """
    使用肤色检测估算脖子位置
    """
    try:
        # 转换到HSV色彩空间
        hsv = cv2.cvtColor(image_rgb, cv2.COLOR_RGB2HSV)

        # 定义肤色范围（HSV）
        lower_skin = np.array([0, 20, 70], dtype=np.uint8)
        upper_skin = np.array([20, 255, 255], dtype=np.uint8)

        # 创建肤色mask
        skin_mask = cv2.inRange(hsv, lower_skin, upper_skin)

        # 形态学操作去噪
        kernel = np.ones((3,3), np.uint8)
        skin_mask = cv2.morphologyEx(skin_mask, cv2.MORPH_OPEN, kernel)
        skin_mask = cv2.morphologyEx(skin_mask, cv2.MORPH_CLOSE, kernel)

        # 找到轮廓
        contours, _ = cv2.findContours(skin_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

        if contours:
            # 找到最大的肤色区域
            largest_contour = max(contours, key=cv2.contourArea)

            # 获取边界框
            x, y, w, h = cv2.boundingRect(largest_contour)

            # 假设这是头部区域，估算脖子位置
            neck_y = y + h + int(h * neck_ratio)

            return neck_y

        return None

    except Exception as e:
        print(f"肤色检测失败: {e}")
        return None

def crop_neck_simple(image_path, output_path, neck_ratio=0.3, safety_margin=20):
    """
    简单的脖子裁剪（保持透明背景）

    Args:
        image_path: 输入图片路径
        output_path: 输出图片路径
        neck_ratio: 脖子位置相对于人脸高度的比例
        safety_margin: 额外的安全边距
    """
    try:
        # 检测人脸并估算脖子位置
        neck_y, original_image, alpha_channel = detect_face_and_estimate_neck(image_path, neck_ratio)

        if neck_y is None:
            return False

        # 添加安全边距
        final_neck_y = neck_y + safety_margin

        # 确保不超出图像边界
        height, width = original_image.shape[:2]
        final_neck_y = min(final_neck_y, height - 10)

        print(f"📍 最终裁剪位置: Y={final_neck_y} (包含{safety_margin}px安全边距)")

        # 裁剪图像：从顶部到脖子位置
        cropped_rgb = original_image[:final_neck_y, :, :]

        # 处理透明通道
        if alpha_channel is not None:
            print("✅ 保持透明背景")
            cropped_alpha = alpha_channel[:final_neck_y, :]
            # 合并RGB和Alpha通道
            cropped_rgba = np.dstack((cropped_rgb, cropped_alpha))
            result_image = Image.fromarray(cropped_rgba, 'RGBA')
        else:
            print("ℹ️ 无透明通道，保存为RGB")
            result_image = Image.fromarray(cropped_rgb, 'RGB')

        # 保存结果
        result_image.save(output_path)

        print(f"✅ 裁剪完成: {output_path}")
        return True

    except Exception as e:
        print(f"❌ 裁剪失败: {e}")
        return False

def batch_crop_simple(input_dir, output_dir, neck_ratio=0.3, safety_margin=20):
    """
    批量简单脖子裁剪
    """
    # 支持的图片格式
    supported_formats = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.webp'}
    
    input_path = Path(input_dir)
    output_path = Path(output_dir)
    
    # 创建输出目录
    output_path.mkdir(parents=True, exist_ok=True)
    
    # 获取所有图片文件
    image_files = []
    for ext in supported_formats:
        image_files.extend(input_path.glob(f'*{ext}'))
        image_files.extend(input_path.glob(f'*{ext.upper()}'))
    
    if not image_files:
        print(f"在 {input_dir} 中没有找到支持的图片文件")
        return
    
    print(f"🎯 简化版脖子裁剪工具")
    print(f"找到 {len(image_files)} 张图片，开始处理...")
    print(f"脖子比例: {neck_ratio}, 安全边距: {safety_margin} 像素")
    print("-" * 50)
    
    success_count = 0
    total_time = 0
    
    for i, img_file in enumerate(image_files, 1):
        start_time = time.time()
        
        # 生成输出文件名
        output_file = output_path / f"{img_file.stem}_neck_cropped_simple.png"
        
        print(f"[{i}/{len(image_files)}] 处理: {img_file.name}")
        
        # 处理图片
        if crop_neck_simple(str(img_file), str(output_file), neck_ratio, safety_margin):
            process_time = time.time() - start_time
            total_time += process_time
            success_count += 1
            print(f"  ✓ 完成 ({process_time:.2f}s) -> {output_file.name}")
        else:
            print(f"  ✗ 失败")
        
        print()
    
    print("-" * 50)
    print(f"处理完成!")
    print(f"成功: {success_count}/{len(image_files)}")
    print(f"总耗时: {total_time:.2f}s")
    print(f"平均耗时: {total_time/len(image_files):.2f}s/张")
    print(f"输出目录: {output_dir}")

def main():
    parser = argparse.ArgumentParser(description='简化版脖子裁剪工具 - 快速人脸检测版')
    parser.add_argument('--input', '-i', help='输入图片文件或目录')
    parser.add_argument('--output', '-o', help='输出文件或目录')
    parser.add_argument('--ratio', '-r', type=float, default=0.3, help='脖子比例 (默认: 0.3)')
    parser.add_argument('--margin', '-m', type=int, default=20, help='安全边距像素数 (默认: 20)')
    
    args = parser.parse_args()
    
    if not args.input or not args.output:
        print("❌ 请指定输入和输出路径")
        print("使用示例:")
        print("  python simple_neck_cropper.py -i input.jpg -o output.png")
        print("  python simple_neck_cropper.py -i input_folder -o output_folder")
        print("  python simple_neck_cropper.py -i input.jpg -o output.png -r 0.4 -m 30")
        return
    
    # 检查输入路径
    if not os.path.exists(args.input):
        print(f"❌ 输入路径不存在: {args.input}")
        return
    
    # 判断是单文件还是目录
    if os.path.isfile(args.input):
        # 单文件处理
        print(f"🎯 处理单个文件: {args.input}")
        if crop_neck_simple(args.input, args.output, args.ratio, args.margin):
            print(f"✅ 处理完成: {args.output}")
        else:
            print("❌ 处理失败")
    else:
        # 批量处理
        batch_crop_simple(args.input, args.output, args.ratio, args.margin)

if __name__ == '__main__':
    main()
