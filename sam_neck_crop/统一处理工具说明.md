# 🎯 统一图像处理工具

## 📋 功能概述

**一站式解决方案**：自动抠图 + 智能脖子裁剪

### 🔄 处理流程
```
输入图片 → RMBG-1.4抠图 → Haar Cascades裁剪 → SAM-B备用裁剪 → 结果分类
```

## 🚀 快速开始

### 方法1：双击启动（推荐）
```bash
双击 "启动统一处理.command" 文件
```

### 方法2：命令行启动
```bash
python3 unified_start.py
```

### 方法3：直接处理
```bash
# 单张图片
python3 unified_processor.py -i "图片.jpg" -o "输出文件夹"

# 批量处理
python3 unified_processor.py -i "图片文件夹" -o "输出文件夹"
```

## 📁 输出结构

```
processed_results/
├── success/                    # 成功处理的图片
│   ├── image1_haar_cropped.png # Haar Cascades成功
│   ├── image2_sam_cropped.png  # SAM-B分析成功
│   └── ...
└── failed/                     # 失败的图片
    ├── image3_matted_only.png  # 仅抠图，未裁剪
    ├── image4_matted_only.png
    └── processing_log.txt      # 失败原因记录
```

## 🎯 技术细节

### 使用的模型
- **RMBG-1.4** (176MB)：抠图模型，去除背景
- **Haar Cascades**：OpenCV人脸检测，快速裁剪
- **SAM-B** (375MB)：Meta分割模型，轮廓分析裁剪

### 处理逻辑
1. **所有图片先抠图**：使用RMBG-1.4去除背景
2. **优先快速裁剪**：Haar Cascades检测人脸位置
3. **备用精确裁剪**：检测失败时用SAM-B分析轮廓
4. **失败图片分类**：无法裁剪的保持抠图效果

### 性能表现
| 处理阶段 | 耗时 | 成功率 |
|----------|------|--------|
| RMBG-1.4抠图 | 8-10秒 | ~95% |
| Haar检测裁剪 | 0.1秒 | ~70% |
| SAM轮廓裁剪 | 1-3秒 | ~90% |
| **总体成功率** | - | **~95%** |

## 🎨 支持格式

### 输入格式
- JPG, JPEG
- PNG
- BMP
- TIFF
- WebP

### 输出格式
- PNG（保持透明背景）

## 💡 使用技巧

### 文件路径输入
- 可以直接拖拽文件/文件夹到终端窗口
- 自动处理路径中的空格和特殊字符

### 批量处理建议
- 建议先用小批量（5-10张）测试效果
- 大批量处理前确保有足够的磁盘空间
- 处理过程中不要关闭终端窗口

### 失败图片处理
- 失败的图片保留抠图效果，可手动裁剪
- 查看 `processing_log.txt` 了解失败原因
- 可以调整参数重新处理失败的图片

## 🔧 故障排除

### 常见问题

**Q: 抠图效果不好？**
A: RMBG-1.4对复杂背景可能效果有限，可以尝试预处理图片

**Q: 人脸检测失败？**
A: 侧身、遮挡、光线问题会导致检测失败，系统会自动切换到SAM-B

**Q: 处理速度慢？**
A: 首次使用需要加载模型，后续会更快

**Q: 内存不足？**
A: SAM-B需要较多内存，建议8GB+内存，或减少批量处理数量

### 依赖检查
```bash
# 检查Python环境
python3 --version

# 检查依赖包
pip list | grep -E "(rembg|opencv|pillow|numpy)"
```

## 📊 处理统计

处理完成后会显示详细统计：
- 总处理数量
- Haar成功数量
- SAM成功数量
- 失败数量及文件列表
- 整体成功率

## 🎉 优势特点

1. **智能化**：自动选择最佳处理方案
2. **高效率**：优先使用快速方法
3. **高成功率**：多重备选方案
4. **结果清晰**：成功失败分类存放
5. **用户友好**：详细的处理日志

---

🎯 **享受智能化的图像处理体验！**
