#!/bin/bash
# SAM脖子裁剪工具快捷启动脚本

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# 进入脚本目录
cd "$SCRIPT_DIR"

echo "🧠 启动SAM脖子裁剪工具..."
echo "Meta最强分割技术 - 精确识别脖子位置"
echo "📁 当前目录: $SCRIPT_DIR"
echo ""

# 检查Python是否安装
if command -v python3 &> /dev/null; then
    echo "✅ Python3 已安装"
    python3 easy_start.py
elif command -v python &> /dev/null; then
    echo "✅ Python 已安装"
    python easy_start.py
else
    echo "❌ 未找到Python，请先安装Python"
    echo "请访问 https://www.python.org/downloads/ 下载安装"
    read -p "按回车键退出..."
    exit 1
fi

echo ""
echo "🎉 SAM脖子裁剪工具运行完成"
read -p "按回车键退出..."
