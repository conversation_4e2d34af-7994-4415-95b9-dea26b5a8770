#!/usr/bin/env python3
"""
调试版脖子裁剪工具
用于测试和调试各种检测方法
"""

import os
import cv2
import numpy as np
from PIL import Image
import argparse

def debug_face_detection(image_path):
    """
    调试人脸检测过程
    """
    print(f"🔍 调试图片: {image_path}")
    
    # 读取图像
    image = cv2.imread(image_path)
    if image is None:
        print(f"❌ 无法读取图像")
        return
    
    image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
    gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
    height, width = image_rgb.shape[:2]
    
    print(f"📏 图像尺寸: {width}x{height}")
    
    # 方法1：正面人脸检测
    print("\n🔍 方法1: 正面人脸检测")
    face_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_frontalface_default.xml')
    faces = face_cascade.detectMultiScale(gray, 1.1, 4)
    print(f"检测到 {len(faces)} 个正面人脸")
    for i, (x, y, w, h) in enumerate(faces):
        print(f"  人脸{i+1}: x={x}, y={y}, w={w}, h={h}")
        neck_y = y + h + int(h * 0.3)
        print(f"  估算脖子位置: Y={neck_y}")
    
    # 方法2：侧面人脸检测
    print("\n🔍 方法2: 侧面人脸检测")
    try:
        profile_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_profileface.xml')
        profile_faces = profile_cascade.detectMultiScale(gray, 1.1, 4)
        print(f"检测到 {len(profile_faces)} 个侧面人脸")
        for i, (x, y, w, h) in enumerate(profile_faces):
            print(f"  侧面人脸{i+1}: x={x}, y={y}, w={w}, h={h}")
            neck_y = y + h + int(h * 0.3)
            print(f"  估算脖子位置: Y={neck_y}")
    except Exception as e:
        print(f"侧面人脸检测失败: {e}")
    
    # 方法3：眼睛检测
    print("\n🔍 方法3: 眼睛检测")
    try:
        eye_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_eye.xml')
        eyes = eye_cascade.detectMultiScale(gray, 1.1, 4)
        print(f"检测到 {len(eyes)} 个眼睛")
        if len(eyes) >= 1:
            # 找到最高的眼睛位置
            highest_eye = min(eyes, key=lambda eye: eye[1])
            x, y, w, h = highest_eye
            print(f"  最高眼睛位置: x={x}, y={y}, w={w}, h={h}")
            # 估算脸部高度和脖子位置
            estimated_face_height = int(h * 8)  # 眼睛通常是脸高的1/8
            neck_y = y + estimated_face_height + int(estimated_face_height * 0.2)
            print(f"  基于眼睛估算脖子位置: Y={neck_y}")
    except Exception as e:
        print(f"眼睛检测失败: {e}")
    
    # 方法4：肤色检测
    print("\n🔍 方法4: 肤色检测")
    try:
        hsv = cv2.cvtColor(image_rgb, cv2.COLOR_RGB2HSV)
        
        # 定义肤色范围
        lower_skin = np.array([0, 20, 70], dtype=np.uint8)
        upper_skin = np.array([20, 255, 255], dtype=np.uint8)
        
        skin_mask = cv2.inRange(hsv, lower_skin, upper_skin)
        
        # 计算肤色像素
        skin_pixels = np.sum(skin_mask > 0)
        total_pixels = width * height
        skin_ratio = skin_pixels / total_pixels
        
        print(f"肤色像素比例: {skin_ratio:.3f}")
        
        # 找到轮廓
        contours, _ = cv2.findContours(skin_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        print(f"检测到 {len(contours)} 个肤色区域")
        
        if contours:
            # 找到最大的肤色区域
            largest_contour = max(contours, key=cv2.contourArea)
            x, y, w, h = cv2.boundingRect(largest_contour)
            area = cv2.contourArea(largest_contour)
            print(f"  最大肤色区域: x={x}, y={y}, w={w}, h={h}, 面积={area}")
            neck_y = y + h + int(h * 0.1)
            print(f"  基于肤色估算脖子位置: Y={neck_y}")
    except Exception as e:
        print(f"肤色检测失败: {e}")
    
    # 方法5：智能比例
    print("\n🔍 方法5: 智能比例估算")
    ratios = [0.25, 0.3, 0.35, 0.4, 0.45]
    for ratio in ratios:
        neck_y = int(height * ratio)
        print(f"  比例 {ratio}: 脖子位置 Y={neck_y}")

def test_crop_with_different_methods(image_path):
    """
    使用不同方法测试裁剪效果
    """
    print(f"\n🧪 测试不同裁剪方法")
    
    # 读取图像
    image = cv2.imread(image_path)
    if image is None:
        print(f"❌ 无法读取图像")
        return
    
    image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
    height, width = image_rgb.shape[:2]
    
    # 测试不同的裁剪位置
    test_positions = [
        ("保守裁剪", int(height * 0.25)),
        ("标准裁剪", int(height * 0.35)),
        ("激进裁剪", int(height * 0.45)),
    ]
    
    for name, crop_y in test_positions:
        try:
            cropped = image_rgb[:crop_y, :, :]
            output_path = f"debug_{name.replace(' ', '_')}.png"
            
            result_image = Image.fromarray(cropped)
            result_image.save(output_path)
            
            print(f"✅ {name} (Y={crop_y}): 保存到 {output_path}")
        except Exception as e:
            print(f"❌ {name} 失败: {e}")

def main():
    parser = argparse.ArgumentParser(description='调试脖子裁剪工具')
    parser.add_argument('--input', '-i', required=True, help='输入图片路径')
    parser.add_argument('--debug', '-d', action='store_true', help='显示调试信息')
    parser.add_argument('--test', '-t', action='store_true', help='测试不同裁剪方法')
    
    args = parser.parse_args()
    
    if not os.path.exists(args.input):
        print(f"❌ 文件不存在: {args.input}")
        return
    
    if args.debug:
        debug_face_detection(args.input)
    
    if args.test:
        test_crop_with_different_methods(args.input)

if __name__ == '__main__':
    main()
