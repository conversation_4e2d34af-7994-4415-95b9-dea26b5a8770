#!/usr/bin/env python3
"""
SAM 脖子裁剪工具
使用 Segment Anything Model 精确识别头部，然后裁剪脖子以下部分
支持侧身、遮挡等复杂场景
"""

import os
import cv2
import numpy as np
from PIL import Image
import argparse
from pathlib import Path
import time
import io

def install_dependencies():
    """检查依赖"""
    print("🔧 检查SAM脖子裁剪工具依赖...")

    # 检查是否能导入必要的包
    try:
        import sys
        sys.path.append('../auto_matting_tool')
        from rembg import remove, new_session
        print("✅ rembg 可用")
    except ImportError:
        print("❌ rembg 不可用，请确保auto_matting_tool中的依赖已安装")
        return False

    try:
        import cv2
        print("✅ opencv-python 可用")
    except ImportError:
        print("❌ opencv-python 不可用")
        return False

    try:
        import numpy as np
        print("✅ numpy 可用")
    except ImportError:
        print("❌ numpy 不可用")
        return False

    try:
        from PIL import Image
        print("✅ pillow 可用")
    except ImportError:
        print("❌ pillow 不可用")
        return False

    print("✅ 所有依赖检查完成")
    return True

def find_neck_position(head_mask, safety_margin=20):
    """
    从头部mask中找到脖子位置
    
    Args:
        head_mask: 头部分割mask
        safety_margin: 安全边距，避免裁剪到脸部
    
    Returns:
        neck_y: 脖子的Y坐标位置
    """
    # 找到头部mask的轮廓
    contours, _ = cv2.findContours(head_mask.astype(np.uint8), cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    
    if not contours:
        return None
    
    # 找到最大的轮廓（主要头部）
    largest_contour = max(contours, key=cv2.contourArea)
    
    # 找到轮廓的最低点
    bottom_points = []
    for point in largest_contour:
        x, y = point[0]
        bottom_points.append((x, y))
    
    # 按Y坐标排序，找到最低的几个点
    bottom_points.sort(key=lambda p: p[1], reverse=True)
    
    # 取最低点的Y坐标作为脖子位置
    if bottom_points:
        neck_y = bottom_points[0][1] + safety_margin
        return neck_y
    
    return None

def segment_head_with_sam(image_path):
    """
    使用现有的SAM抠图工具进行头部分割

    Args:
        image_path: 输入图片路径

    Returns:
        head_mask: 头部分割mask
        original_image: 原始图像
    """
    try:
        # 使用现有的SAM抠图工具
        import sys
        sys.path.append('../auto_matting_tool')
        from rembg import remove, new_session

        # 使用SAM模型进行抠图
        session = new_session('sam')

        # 读取原图
        with open(image_path, 'rb') as f:
            input_data = f.read()

        print("🧠 使用SAM进行抠图...")
        # 使用SAM进行抠图
        output_data = remove(input_data, session=session)

        # 转换为PIL图像
        img_with_alpha = Image.open(io.BytesIO(output_data)).convert('RGBA')
        original_img = Image.open(image_path).convert('RGB')

        # 获取alpha通道作为mask
        alpha_array = np.array(img_with_alpha)[:, :, 3]
        original_array = np.array(original_img)

        # 将alpha通道转换为二值mask
        head_mask = (alpha_array > 128).astype(np.uint8) * 255

        print("✅ SAM抠图完成，提取头部区域")
        return head_mask, original_array
        
    except Exception as e:
        print(f"❌ SAM分割失败: {e}")
        return None, None

def crop_neck_and_below(image_path, output_path, safety_margin=20):
    """
    裁剪脖子以下部分，只保留头部和脖子
    
    Args:
        image_path: 输入图片路径
        output_path: 输出图片路径
        safety_margin: 安全边距
    """
    try:
        # 使用SAM分割头部
        head_mask, original_image = segment_head_with_sam(image_path)
        
        if head_mask is None:
            return False
        
        # 找到脖子位置
        neck_y = find_neck_position(head_mask, safety_margin)
        
        if neck_y is None:
            print("❌ 未能确定脖子位置")
            return False
        
        print(f"📍 脖子位置: Y={neck_y}")
        
        # 裁剪图像：从顶部到脖子位置
        cropped_image = original_image[:neck_y, :, :]
        
        # 保存结果
        result_image = Image.fromarray(cropped_image)
        result_image.save(output_path)
        
        print(f"✅ 裁剪完成: {output_path}")
        return True
        
    except Exception as e:
        print(f"❌ 裁剪失败: {e}")
        return False

def batch_crop_neck(input_dir, output_dir, safety_margin=20):
    """
    批量裁剪脖子以下部分
    
    Args:
        input_dir: 输入目录
        output_dir: 输出目录
        safety_margin: 安全边距
    """
    # 支持的图片格式
    supported_formats = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.webp'}
    
    input_path = Path(input_dir)
    output_path = Path(output_dir)
    
    # 创建输出目录
    output_path.mkdir(parents=True, exist_ok=True)
    
    # 获取所有图片文件
    image_files = []
    for ext in supported_formats:
        image_files.extend(input_path.glob(f'*{ext}'))
        image_files.extend(input_path.glob(f'*{ext.upper()}'))
    
    if not image_files:
        print(f"在 {input_dir} 中没有找到支持的图片文件")
        return
    
    print(f"🎯 SAM脖子裁剪工具")
    print(f"找到 {len(image_files)} 张图片，开始处理...")
    print(f"安全边距: {safety_margin} 像素")
    print("-" * 50)
    
    success_count = 0
    total_time = 0
    
    for i, img_file in enumerate(image_files, 1):
        start_time = time.time()
        
        # 生成输出文件名
        output_file = output_path / f"{img_file.stem}_neck_cropped.png"
        
        print(f"[{i}/{len(image_files)}] 处理: {img_file.name}")
        
        # 处理图片
        if crop_neck_and_below(str(img_file), str(output_file), safety_margin):
            process_time = time.time() - start_time
            total_time += process_time
            success_count += 1
            print(f"  ✓ 完成 ({process_time:.2f}s) -> {output_file.name}")
        else:
            print(f"  ✗ 失败")
        
        print()
    
    print("-" * 50)
    print(f"处理完成!")
    print(f"成功: {success_count}/{len(image_files)}")
    print(f"总耗时: {total_time:.2f}s")
    print(f"平均耗时: {total_time/len(image_files):.2f}s/张")
    print(f"输出目录: {output_dir}")

def main():
    parser = argparse.ArgumentParser(description='SAM脖子裁剪工具 - 精确识别并裁剪脖子以下部分')
    parser.add_argument('--input', '-i', help='输入图片文件或目录')
    parser.add_argument('--output', '-o', help='输出文件或目录')
    parser.add_argument('--margin', '-m', type=int, default=20, help='安全边距像素数 (默认: 20)')
    parser.add_argument('--install', action='store_true', help='安装依赖包和下载模型')
    
    args = parser.parse_args()
    
    if args.install:
        print("🔧 检查SAM脖子裁剪工具...")
        if install_dependencies():
            print("✅ 工具检查完成，可以使用")
        else:
            print("❌ 依赖检查失败")
        return
    
    if not args.input or not args.output:
        print("❌ 请指定输入和输出路径")
        print("使用示例:")
        print("  python sam_neck_cropper.py -i input.jpg -o output.png")
        print("  python sam_neck_cropper.py -i input_folder -o output_folder")
        print("  python sam_neck_cropper.py --install  # 安装依赖")
        return
    
    # 检查输入路径
    if not os.path.exists(args.input):
        print(f"❌ 输入路径不存在: {args.input}")
        return
    
    # 判断是单文件还是目录
    if os.path.isfile(args.input):
        # 单文件处理
        print(f"🎯 处理单个文件: {args.input}")
        if crop_neck_and_below(args.input, args.output, args.margin):
            print(f"✅ 处理完成: {args.output}")
        else:
            print("❌ 处理失败")
    else:
        # 批量处理
        batch_crop_neck(args.input, args.output, args.margin)

if __name__ == '__main__':
    main()
