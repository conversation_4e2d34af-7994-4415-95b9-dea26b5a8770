#!/usr/bin/env python3
"""
SAM脖子裁剪工具 - 简化启动界面
"""

import os
import sys
from pathlib import Path

def process_single_image():
    """处理单张图片"""
    print("\n🖼️ 单张图片脖子裁剪")
    print("💡 提示：可以直接拖拽图片文件到终端窗口来输入路径")
    
    input_path = input("输入图片路径: ").strip()
    
    # 处理路径中的特殊字符
    if input_path.startswith('"') and input_path.endswith('"'):
        input_path = input_path[1:-1]  # 去掉首尾引号
    
    # 处理转义字符
    input_path = input_path.replace('\\ ', ' ')
    input_path = input_path.replace('\\(', '(')
    input_path = input_path.replace('\\)', ')')
    
    if not input_path:
        print("❌ 输入路径不能为空")
        return
        
    print(f"📝 处理后的路径: {input_path}")
        
    # 检查文件是否存在
    if not os.path.exists(input_path):
        print(f"❌ 文件不存在: {input_path}")
        print("💡 请检查路径是否正确，或重新拖拽文件到终端")
        return
    
    # 生成输出路径
    input_file = Path(input_path)
    output_path = str(input_file.parent / f"{input_file.stem}_neck_cropped.png")
    
    # 询问安全边距
    margin_input = input("安全边距像素 (默认20，回车使用默认): ").strip()
    margin = 20
    if margin_input:
        try:
            margin = int(margin_input)
        except ValueError:
            print("❌ 无效数字，使用默认值20")
    
    print(f"🎛️ 使用设置: {margin}像素安全边距")
    print(f"📁 输出到: {output_path}")
    
    # 获取脚本目录
    script_dir = os.path.dirname(os.path.abspath(__file__))
    
    cmd = f"cd '{script_dir}' && python3 sam_neck_cropper.py -i '{input_path}' -o '{output_path}' -m {margin}"
    print("⏳ 处理中... (首次使用会下载SAM模型)")
    result = os.system(cmd)
    
    if result == 0:
        print("✅ 脖子裁剪完成!")
        # 自动打开结果目录
        import subprocess
        subprocess.run(['open', os.path.dirname(output_path)])
    else:
        print("❌ 处理失败")

def process_batch():
    """批量处理"""
    print("\n📁 批量脖子裁剪")
    print("💡 提示：可以直接拖拽文件夹到终端窗口来输入路径")
    
    input_path = input("输入图片文件夹路径: ").strip()
    
    # 处理路径中的特殊字符
    if input_path.startswith('"') and input_path.endswith('"'):
        input_path = input_path[1:-1]
    
    input_path = input_path.replace('\\ ', ' ')
    input_path = input_path.replace('\\(', '(')
    input_path = input_path.replace('\\)', ')')
    
    if not input_path:
        print("❌ 输入路径不能为空")
        return
    
    print(f"📝 处理后的路径: {input_path}")
    
    # 检查文件夹是否存在
    if not os.path.exists(input_path):
        print(f"❌ 文件夹不存在: {input_path}")
        print("💡 请检查路径是否正确，或重新拖拽文件夹到终端")
        return
    
    # 生成输出路径
    output_path = f"{input_path}_neck_cropped"
    
    # 询问安全边距
    margin_input = input("安全边距像素 (默认20，回车使用默认): ").strip()
    margin = 20
    if margin_input:
        try:
            margin = int(margin_input)
        except ValueError:
            print("❌ 无效数字，使用默认值20")
    
    print(f"🎛️ 使用设置: {margin}像素安全边距")
    print(f"📁 输出到: {output_path}")
    
    # 获取脚本目录
    script_dir = os.path.dirname(os.path.abspath(__file__))
    
    cmd = f"cd '{script_dir}' && python3 sam_neck_cropper.py -i '{input_path}' -o '{output_path}' -m {margin}"
    print("⏳ 批量处理中... (首次使用会下载SAM模型)")
    result = os.system(cmd)
    
    if result == 0:
        print("✅ 批量脖子裁剪完成!")
        # 自动打开结果目录
        import subprocess
        subprocess.run(['open', output_path])
    else:
        print("❌ 处理失败")

def install_tool():
    """安装工具"""
    print("\n🔧 安装SAM脖子裁剪工具")
    print("这将安装必要的依赖包并下载SAM模型 (375MB)")
    
    confirm = input("确认安装? (y/N): ").strip().lower()
    if confirm != 'y':
        print("❌ 安装取消")
        return
    
    # 获取脚本目录
    script_dir = os.path.dirname(os.path.abspath(__file__))
    
    cmd = f"cd '{script_dir}' && python3 sam_neck_cropper.py --install"
    print("⏳ 安装中...")
    result = os.system(cmd)
    
    if result == 0:
        print("✅ 安装完成!")
    else:
        print("❌ 安装失败")

def show_help():
    """显示帮助信息"""
    print("\n📖 SAM脖子裁剪工具使用说明")
    print("=" * 50)
    print("🎯 功能：使用SAM模型精确识别头部，裁剪脖子以下部分")
    print("🚀 优势：支持侧身、遮挡等复杂场景")
    print()
    print("📋 使用步骤：")
    print("1. 首次使用请先选择 '4. 安装工具'")
    print("2. 选择 '1. 单张图片' 或 '2. 批量处理'")
    print("3. 拖拽文件/文件夹到终端输入路径")
    print("4. 设置安全边距（推荐20像素）")
    print("5. 等待处理完成")
    print()
    print("⚙️ 参数说明：")
    print("- 安全边距：在检测到的脖子位置下方额外保留的像素")
    print("- 推荐值：20像素（避免裁剪到下巴）")
    print()
    print("🎨 输出格式：PNG（保持透明度）")
    print("📁 输出位置：原文件同目录，文件名加 '_neck_cropped' 后缀")

def process_single_simple():
    """简化版单张图片处理"""
    print("\n🖼️ 单张图片脖子裁剪 (简化版)")
    print("💡 提示：可以直接拖拽图片文件到终端窗口来输入路径")

    input_path = input("输入图片路径: ").strip()

    # 处理路径中的特殊字符
    if input_path.startswith('"') and input_path.endswith('"'):
        input_path = input_path[1:-1]

    input_path = input_path.replace('\\ ', ' ')
    input_path = input_path.replace('\\(', '(')
    input_path = input_path.replace('\\)', ')')

    if not input_path:
        print("❌ 输入路径不能为空")
        return

    print(f"📝 处理后的路径: {input_path}")

    # 检查文件是否存在
    if not os.path.exists(input_path):
        print(f"❌ 文件不存在: {input_path}")
        return

    # 生成输出路径
    input_file = Path(input_path)
    output_path = str(input_file.parent / f"{input_file.stem}_neck_cropped_simple.png")

    # 询问参数
    margin_input = input("安全边距像素 (默认20): ").strip()
    margin = 20
    if margin_input:
        try:
            margin = int(margin_input)
        except ValueError:
            print("❌ 无效数字，使用默认值20")

    ratio_input = input("脖子比例 (默认0.3): ").strip()
    ratio = 0.3
    if ratio_input:
        try:
            ratio = float(ratio_input)
        except ValueError:
            print("❌ 无效数字，使用默认值0.3")

    print(f"🎛️ 使用设置: 安全边距{margin}px, 脖子比例{ratio}")
    print(f"📁 输出到: {output_path}")

    # 获取脚本目录
    script_dir = os.path.dirname(os.path.abspath(__file__))

    cmd = f"cd '{script_dir}' && python3 simple_neck_cropper.py -i '{input_path}' -o '{output_path}' -m {margin} -r {ratio}"
    print("⏳ 处理中...")
    result = os.system(cmd)

    if result == 0:
        print("✅ 脖子裁剪完成!")
        import subprocess
        subprocess.run(['open', os.path.dirname(output_path)])
    else:
        print("❌ 处理失败")

def process_batch_simple():
    """简化版批量处理"""
    print("\n📁 批量脖子裁剪 (简化版)")
    print("💡 提示：可以直接拖拽文件夹到终端窗口来输入路径")

    input_path = input("输入图片文件夹路径: ").strip()

    # 处理路径中的特殊字符
    if input_path.startswith('"') and input_path.endswith('"'):
        input_path = input_path[1:-1]

    input_path = input_path.replace('\\ ', ' ')
    input_path = input_path.replace('\\(', '(')
    input_path = input_path.replace('\\)', ')')

    if not input_path:
        print("❌ 输入路径不能为空")
        return

    print(f"📝 处理后的路径: {input_path}")

    # 检查文件夹是否存在
    if not os.path.exists(input_path):
        print(f"❌ 文件夹不存在: {input_path}")
        return

    # 生成输出路径
    output_path = f"{input_path}_neck_cropped_simple"

    # 询问参数
    margin_input = input("安全边距像素 (默认20): ").strip()
    margin = 20
    if margin_input:
        try:
            margin = int(margin_input)
        except ValueError:
            print("❌ 无效数字，使用默认值20")

    ratio_input = input("脖子比例 (默认0.3): ").strip()
    ratio = 0.3
    if ratio_input:
        try:
            ratio = float(ratio_input)
        except ValueError:
            print("❌ 无效数字，使用默认值0.3")

    print(f"🎛️ 使用设置: 安全边距{margin}px, 脖子比例{ratio}")
    print(f"📁 输出到: {output_path}")

    # 获取脚本目录
    script_dir = os.path.dirname(os.path.abspath(__file__))

    cmd = f"cd '{script_dir}' && python3 simple_neck_cropper.py -i '{input_path}' -o '{output_path}' -m {margin} -r {ratio}"
    print("⏳ 批量处理中...")
    result = os.system(cmd)

    if result == 0:
        print("✅ 批量脖子裁剪完成!")
        import subprocess
        subprocess.run(['open', output_path])
    else:
        print("❌ 处理失败")

def main():
    print("🎯 脖子裁剪工具")
    print("智能识别 + 精确裁剪")
    print("=" * 50)

    while True:
        print("\n请选择操作:")
        print("1. 🧠 单张图片脖子裁剪 (SAM版 - 智能分析)")
        print("   • PNG抠图：直接分析透明区域")
        print("   • 普通图片：可选择是否先抠图")
        print("2. 📁 批量脖子裁剪 (SAM版)")
        print("3. ⚡ 单张图片脖子裁剪 (快速版 - 推荐)")
        print("   • 人脸检测 + 几何计算，速度快")
        print("4. ⚡ 批量脖子裁剪 (快速版)")
        print("5. 📖 使用说明")
        print("6. 🔧 检查工具")
        print("7. 🚪 退出")

        choice = input("\n请输入选项 (1-7): ").strip()

        if choice == "1":
            process_single_image()

        elif choice == "2":
            process_batch()

        elif choice == "3":
            process_single_simple()

        elif choice == "4":
            process_batch_simple()

        elif choice == "5":
            show_help()

        elif choice == "6":
            install_tool()

        elif choice == "7":
            print("👋 再见!")
            break

        else:
            print("❌ 无效选项，请输入1-7")
        
        # 询问是否继续
        print("\n" + "="*50)
        continue_choice = input("继续使用? (回车继续, q退出): ").strip().lower()
        if continue_choice == 'q':
            print("👋 再见!")
            break

if __name__ == "__main__":
    main()
