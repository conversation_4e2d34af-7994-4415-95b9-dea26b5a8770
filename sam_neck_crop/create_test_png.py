#!/usr/bin/env python3
"""
创建测试PNG文件（带透明背景）
"""

import numpy as np
from PIL import Image

def create_test_transparent_image():
    """创建一个带透明背景的测试图像"""
    
    # 创建一个400x600的RGBA图像
    width, height = 400, 600
    
    # 创建透明背景
    image = np.zeros((height, width, 4), dtype=np.uint8)
    
    # 绘制一个简单的人像轮廓（椭圆形头部）
    center_x, center_y = width // 2, height // 3
    
    # 头部（椭圆）
    for y in range(height):
        for x in range(width):
            # 椭圆公式
            dx = (x - center_x) / 80
            dy = (y - center_y) / 100
            
            if dx*dx + dy*dy <= 1:  # 头部
                image[y, x] = [220, 180, 140, 255]  # 肤色，不透明
            elif dx*dx + dy*dy <= 1.2 and y > center_y + 50:  # 脖子部分
                image[y, x] = [220, 180, 140, 255]  # 肤色，不透明
            elif y > center_y + 120 and abs(x - center_x) < 60:  # 身体部分
                image[y, x] = [100, 100, 200, 255]  # 衣服颜色，不透明
    
    # 转换为PIL图像
    pil_image = Image.fromarray(image, 'RGBA')
    
    # 保存
    pil_image.save('test_transparent_person.png')
    print("✅ 创建测试透明PNG: test_transparent_person.png")
    print(f"📏 尺寸: {width}x{height}")
    print("🎯 包含: 头部、脖子、身体部分，透明背景")

if __name__ == '__main__':
    create_test_transparent_image()
