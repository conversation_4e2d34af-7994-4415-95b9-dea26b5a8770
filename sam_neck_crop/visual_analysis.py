#!/usr/bin/env python3
"""
可视化分析工具 - 直接看图片内容
"""

import cv2
import numpy as np
from PIL import Image
import matplotlib.pyplot as plt
import argparse

def analyze_image_visually(image_path):
    """
    可视化分析图片，直接显示内容和透明区域
    """
    print(f"🔍 分析图片: {image_path}")
    
    # 读取图片
    pil_image = Image.open(image_path)
    print(f"📏 图像模式: {pil_image.mode}")
    print(f"📏 图像尺寸: {pil_image.size}")
    
    if pil_image.mode == 'RGBA':
        # 转换为numpy数组
        img_array = np.array(pil_image)
        rgb = img_array[:, :, :3]
        alpha = img_array[:, :, 3]
        
        print(f"🔍 Alpha通道统计:")
        print(f"  最小值: {alpha.min()}")
        print(f"  最大值: {alpha.max()}")
        print(f"  平均值: {alpha.mean():.1f}")
        
        # 统计透明度分布
        transparent_pixels = np.sum(alpha == 0)
        semi_transparent = np.sum((alpha > 0) & (alpha < 255))
        opaque_pixels = np.sum(alpha == 255)
        total_pixels = alpha.size
        
        print(f"🎯 透明度分布:")
        print(f"  完全透明: {transparent_pixels} ({transparent_pixels/total_pixels*100:.1f}%)")
        print(f"  半透明: {semi_transparent} ({semi_transparent/total_pixels*100:.1f}%)")
        print(f"  不透明: {opaque_pixels} ({opaque_pixels/total_pixels*100:.1f}%)")
        
        # 找到内容区域的边界
        non_transparent = alpha > 0
        if np.any(non_transparent):
            rows = np.any(non_transparent, axis=1)
            cols = np.any(non_transparent, axis=0)
            
            top = np.argmax(rows)
            bottom = len(rows) - np.argmax(rows[::-1]) - 1
            left = np.argmax(cols)
            right = len(cols) - np.argmax(cols[::-1]) - 1
            
            content_height = bottom - top + 1
            content_width = right - left + 1
            
            print(f"📦 内容区域边界:")
            print(f"  顶部: Y={top}")
            print(f"  底部: Y={bottom}")
            print(f"  左侧: X={left}")
            print(f"  右侧: X={right}")
            print(f"  内容尺寸: {content_width}x{content_height}")
            
            # 分析内容在图像中的位置
            height, width = alpha.shape
            content_center_y = (top + bottom) / 2
            relative_position = content_center_y / height
            
            print(f"🎯 内容位置分析:")
            print(f"  内容中心Y: {content_center_y:.1f}")
            print(f"  相对位置: {relative_position:.1%} (0%=顶部, 50%=中间, 100%=底部)")
            
            # 建议裁剪位置
            if relative_position < 0.3:
                print("💡 建议: 内容偏上，可能是头像，建议保守裁剪")
                suggested_crop = int(bottom + content_height * 0.1)
            elif relative_position < 0.6:
                print("💡 建议: 内容居中，可能是半身照，建议适中裁剪")
                suggested_crop = int(bottom + content_height * 0.2)
            else:
                print("💡 建议: 内容偏下，可能是全身照，建议激进裁剪")
                suggested_crop = int(bottom + content_height * 0.3)
            
            suggested_crop = min(suggested_crop, height - 10)
            print(f"🎯 建议裁剪位置: Y={suggested_crop}")
            print(f"📊 裁剪后尺寸: {width}x{suggested_crop}")
            
            return suggested_crop
        else:
            print("❌ 图像完全透明!")
            return None
    else:
        print("ℹ️ 非RGBA图像，无法分析透明度")
        return None

def manual_crop(image_path, output_path, crop_y):
    """
    手动裁剪到指定位置
    """
    try:
        pil_image = Image.open(image_path)
        
        if pil_image.mode == 'RGBA':
            img_array = np.array(pil_image)
            cropped = img_array[:crop_y, :, :]
            result = Image.fromarray(cropped, 'RGBA')
        else:
            img_array = np.array(pil_image)
            cropped = img_array[:crop_y, :, :]
            result = Image.fromarray(cropped)
        
        result.save(output_path)
        print(f"✅ 手动裁剪完成: {output_path}")
        print(f"📊 裁剪到: Y={crop_y}")
        return True
    except Exception as e:
        print(f"❌ 裁剪失败: {e}")
        return False

def main():
    parser = argparse.ArgumentParser(description='可视化分析图片')
    parser.add_argument('--input', '-i', required=True, help='输入图片路径')
    parser.add_argument('--output', '-o', help='输出图片路径')
    parser.add_argument('--crop', '-c', type=int, help='手动指定裁剪位置')
    
    args = parser.parse_args()
    
    # 分析图片
    suggested_crop = analyze_image_visually(args.input)
    
    # 如果指定了输出路径
    if args.output:
        if args.crop:
            # 使用手动指定的裁剪位置
            manual_crop(args.input, args.output, args.crop)
        elif suggested_crop:
            # 使用建议的裁剪位置
            manual_crop(args.input, args.output, suggested_crop)
        else:
            print("❌ 无法确定裁剪位置")

if __name__ == '__main__':
    main()
