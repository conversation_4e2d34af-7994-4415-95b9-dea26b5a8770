#!/usr/bin/env python3
"""
智能脖子裁剪工具
自动识别图片类型并调整裁剪策略
"""

import os
import cv2
import numpy as np
from PIL import Image
import argparse
from pathlib import Path
import time

def analyze_image_type(image_rgb, faces):
    """
    分析图片类型：全身照、半身照、头像
    
    Returns:
        image_type: 'fullbody', 'halfbody', 'headshot'
        crop_strategy: 裁剪策略
    """
    height, width = image_rgb.shape[:2]
    
    if len(faces) == 0:
        # 没有检测到人脸，可能是全身照或者角度问题
        return 'unknown', 'conservative'
    
    # 分析最大人脸
    largest_face = max(faces, key=lambda face: face[2] * face[3])
    x, y, w, h = largest_face
    
    # 计算人脸相对于图像的比例
    face_height_ratio = h / height
    face_width_ratio = w / width
    face_y_ratio = y / height  # 人脸在图像中的垂直位置
    
    print(f"📊 图像分析:")
    print(f"  人脸高度比例: {face_height_ratio:.3f}")
    print(f"  人脸宽度比例: {face_width_ratio:.3f}")
    print(f"  人脸位置比例: {face_y_ratio:.3f}")
    
    # 判断图片类型
    if face_height_ratio > 0.4:
        # 人脸占图像高度40%以上，可能是头像
        image_type = 'headshot'
        crop_strategy = 'minimal'
        print("🎯 识别为: 头像/特写")
    elif face_height_ratio > 0.25 and face_y_ratio < 0.4:
        # 人脸占25-40%且位置较高，可能是半身照
        image_type = 'halfbody'
        crop_strategy = 'moderate'
        print("🎯 识别为: 半身照")
    elif face_height_ratio < 0.25:
        # 人脸占比小，可能是全身照
        image_type = 'fullbody'
        crop_strategy = 'aggressive'
        print("🎯 识别为: 全身照")
    else:
        # 其他情况
        image_type = 'unknown'
        crop_strategy = 'moderate'
        print("🎯 识别为: 未知类型")
    
    return image_type, crop_strategy

def smart_neck_detection(image_path, crop_strategy='moderate'):
    """
    智能脖子检测
    """
    try:
        # 读取图像（保持透明通道）
        from PIL import Image as PILImage
        pil_image = PILImage.open(image_path)

        # 检查是否有透明通道
        has_alpha = pil_image.mode in ('RGBA', 'LA') or 'transparency' in pil_image.info
        print(f"🔍 图像模式: {pil_image.mode}, 有透明通道: {has_alpha}")

        if has_alpha:
            # 保持RGBA格式
            image_rgba = np.array(pil_image.convert('RGBA'))
            image_rgb = image_rgba[:, :, :3]  # RGB部分用于检测
            alpha_channel = image_rgba[:, :, 3]  # 保存透明通道
        else:
            # 没有透明通道，转换为RGB
            image_rgb = np.array(pil_image.convert('RGB'))
            alpha_channel = None

        # 转换为灰度图用于人脸检测
        gray = cv2.cvtColor(image_rgb, cv2.COLOR_RGB2GRAY)
        height, width = image_rgb.shape[:2]
        
        print(f"📏 图像尺寸: {width}x{height}")
        
        # 多种人脸检测
        face_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_frontalface_default.xml')
        faces = face_cascade.detectMultiScale(gray, 1.1, 4)
        
        # 尝试侧面人脸检测
        if len(faces) == 0:
            try:
                profile_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_profileface.xml')
                faces = profile_cascade.detectMultiScale(gray, 1.1, 4)
                print("✅ 使用侧面人脸检测")
            except:
                pass
        
        # 分析图片类型
        image_type, strategy = analyze_image_type(image_rgb, faces)
        
        # 根据策略调整裁剪位置
        if len(faces) > 0:
            largest_face = max(faces, key=lambda face: face[2] * face[3])
            x, y, w, h = largest_face
            
            print(f"✅ 检测到人脸: x={x}, y={y}, w={w}, h={h}")
            
            # 根据图片类型和策略计算脖子位置
            if strategy == 'minimal':
                # 头像：只裁剪很少部分
                neck_ratio = 0.1
                safety_margin = -10  # 负数表示向上偏移
            elif strategy == 'moderate':
                # 半身照：适中裁剪
                neck_ratio = 0.2
                safety_margin = 0
            elif strategy == 'aggressive':
                # 全身照：更多裁剪
                neck_ratio = 0.4
                safety_margin = 20
            else:
                neck_ratio = 0.3
                safety_margin = 10
            
            neck_y = y + h + int(h * neck_ratio) + safety_margin
            
            # 确保不超出图像边界
            neck_y = max(y + h, min(neck_y, height - 10))
            
            print(f"📍 计算脖子位置: Y={neck_y} (策略: {strategy})")
            
        else:
            print("⚠️ 未检测到人脸，使用智能估算")
            # 根据图像比例智能估算
            if height > width * 1.5:
                # 竖直图像，可能是全身照
                neck_y = int(height * 0.3)
            else:
                # 横向或方形图像，可能是半身照
                neck_y = int(height * 0.6)
        
        return neck_y, image_rgb, image_type, alpha_channel
        
    except Exception as e:
        print(f"❌ 智能检测失败: {e}")
        return None, None, None

def smart_crop_neck(image_path, output_path, force_strategy=None):
    """
    智能脖子裁剪
    """
    try:
        neck_y, original_image, image_type, alpha_channel = smart_neck_detection(image_path, force_strategy)

        if neck_y is None:
            return False

        height, width = original_image.shape[:2]

        # 检查是否需要裁剪
        crop_ratio = neck_y / height
        if crop_ratio > 0.9:
            print(f"⚠️ 裁剪比例过高 ({crop_ratio:.2f})，可能已经是头像")
            print("💡 建议：这张图片可能不需要裁剪，或者尝试更激进的策略")

            # 提供选择
            response = input("是否仍要裁剪? (y/N): ").strip().lower()
            if response != 'y':
                print("❌ 用户取消裁剪")
                return False

        print(f"✂️ 裁剪位置: Y={neck_y} (保留 {crop_ratio:.1%} 的图像)")

        # 裁剪图像（保持透明背景）
        cropped_rgb = original_image[:neck_y, :, :]

        # 处理透明通道
        if alpha_channel is not None:
            print("✅ 保持透明背景")
            cropped_alpha = alpha_channel[:neck_y, :]
            # 合并RGB和Alpha通道
            cropped_rgba = np.dstack((cropped_rgb, cropped_alpha))
            result_image = Image.fromarray(cropped_rgba, 'RGBA')
        else:
            print("ℹ️ 无透明通道，保存为RGB")
            result_image = Image.fromarray(cropped_rgb, 'RGB')

        # 保存结果
        result_image.save(output_path)

        print(f"✅ 智能裁剪完成: {output_path}")
        print(f"📊 原始尺寸: {width}x{height} → 裁剪后: {width}x{neck_y}")

        return True
        
    except Exception as e:
        print(f"❌ 裁剪失败: {e}")
        return False

def main():
    parser = argparse.ArgumentParser(description='智能脖子裁剪工具')
    parser.add_argument('--input', '-i', required=True, help='输入图片路径')
    parser.add_argument('--output', '-o', required=True, help='输出图片路径')
    parser.add_argument('--strategy', '-s', choices=['minimal', 'moderate', 'aggressive'], 
                       help='强制使用指定策略')
    
    args = parser.parse_args()
    
    if not os.path.exists(args.input):
        print(f"❌ 输入文件不存在: {args.input}")
        return
    
    print(f"🧠 智能脖子裁剪工具")
    print(f"📁 输入: {args.input}")
    print(f"📁 输出: {args.output}")
    if args.strategy:
        print(f"🎯 强制策略: {args.strategy}")
    print("-" * 50)
    
    success = smart_crop_neck(args.input, args.output, args.strategy)
    
    if success:
        print("🎉 处理完成!")
    else:
        print("❌ 处理失败")

if __name__ == '__main__':
    main()
