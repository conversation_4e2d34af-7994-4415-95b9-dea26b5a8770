#!/usr/bin/env python3
"""
统一处理工具启动界面
"""

import os
import sys
from pathlib import Path

def main():
    print("🎯 统一图像处理工具")
    print("流程：RMBG-1.4抠图 → Haar裁剪 → SAM备用裁剪 → 失败分类")
    print("=" * 60)
    
    while True:
        print("\n请选择操作:")
        print("1. 📷 单张图片处理")
        print("2. 📁 批量文件夹处理")
        print("3. 🔧 查看使用说明")
        print("4. 🚪 退出")
        
        choice = input("\n请输入选项 (1-4): ").strip()
        
        if choice == "1":
            process_single()
        elif choice == "2":
            process_batch()
        elif choice == "3":
            show_help()
        elif choice == "4":
            print("👋 再见!")
            break
        else:
            print("❌ 无效选项，请重新选择")

def process_single():
    """处理单张图片"""
    print("\n📷 单张图片处理")
    print("💡 提示：可以直接拖拽图片文件到终端窗口")
    
    input_path = input("输入图片路径: ").strip()
    
    # 处理路径中的引号
    if input_path.startswith('"') and input_path.endswith('"'):
        input_path = input_path[1:-1]
    
    # 处理转义字符
    input_path = input_path.replace('\\ ', ' ')
    
    if not os.path.exists(input_path):
        print(f"❌ 文件不存在: {input_path}")
        return
    
    # 生成输出目录
    input_dir = os.path.dirname(input_path)
    output_dir = os.path.join(input_dir, "processed_results")
    
    print(f"📤 输出目录: {output_dir}")
    
    confirm = input("确认开始处理? (y/N): ").strip().lower()
    if confirm != 'y':
        print("❌ 处理取消")
        return
    
    # 执行处理
    script_dir = os.path.dirname(os.path.abspath(__file__))
    cmd = f"cd '{script_dir}' && python3 unified_processor.py -i '{input_path}' -o '{output_dir}'"
    
    print("⏳ 处理中...")
    result = os.system(cmd)
    
    if result == 0:
        print("🎉 处理完成!")
        # 询问是否打开结果文件夹
        open_folder = input("是否打开结果文件夹? (y/N): ").strip().lower()
        if open_folder == 'y':
            os.system(f"open '{output_dir}'")
    else:
        print("❌ 处理失败")

def process_batch():
    """批量处理文件夹"""
    print("\n📁 批量文件夹处理")
    print("💡 提示：可以直接拖拽文件夹到终端窗口")
    
    input_path = input("输入文件夹路径: ").strip()
    
    # 处理路径中的引号
    if input_path.startswith('"') and input_path.endswith('"'):
        input_path = input_path[1:-1]
    
    # 处理转义字符
    input_path = input_path.replace('\\ ', ' ')
    
    if not os.path.exists(input_path):
        print(f"❌ 文件夹不存在: {input_path}")
        return
    
    if not os.path.isdir(input_path):
        print(f"❌ 不是文件夹: {input_path}")
        return
    
    # 生成输出目录
    output_dir = os.path.join(input_path, "processed_results")
    
    print(f"📤 输出目录: {output_dir}")
    
    confirm = input("确认开始批量处理? (y/N): ").strip().lower()
    if confirm != 'y':
        print("❌ 处理取消")
        return
    
    # 执行处理
    script_dir = os.path.dirname(os.path.abspath(__file__))
    cmd = f"cd '{script_dir}' && python3 unified_processor.py -i '{input_path}' -o '{output_dir}'"
    
    print("⏳ 批量处理中...")
    result = os.system(cmd)
    
    if result == 0:
        print("🎉 批量处理完成!")
        # 询问是否打开结果文件夹
        open_folder = input("是否打开结果文件夹? (y/N): ").strip().lower()
        if open_folder == 'y':
            os.system(f"open '{output_dir}'")
    else:
        print("❌ 批量处理失败")

def show_help():
    """显示使用说明"""
    print("\n📖 使用说明")
    print("=" * 50)
    print("🎯 功能：智能抠图+脖子裁剪")
    print()
    print("📋 处理流程：")
    print("1. RMBG-1.4模型抠图（去除背景）")
    print("2. Haar Cascades快速检测人脸位置")
    print("3. 如果检测失败，使用SAM-B轮廓分析")
    print("4. 成功的保存到success/，失败的保存到failed/")
    print()
    print("📁 输出结构：")
    print("processed_results/")
    print("├── success/          # 成功裁剪的图片")
    print("│   ├── xxx_haar_cropped.png")
    print("│   └── xxx_sam_cropped.png")
    print("└── failed/           # 失败的图片（仅抠图）")
    print("    ├── xxx_matted_only.png")
    print("    └── processing_log.txt")
    print()
    print("🎨 支持格式：")
    print("输入：JPG, PNG, BMP, TIFF, WebP")
    print("输出：PNG（保持透明背景）")
    print()
    print("⚡ 性能：")
    print("- Haar检测：0.1秒/张（快速）")
    print("- SAM分析：1-3秒/张（精确）")
    print("- RMBG抠图：8-10秒/张")
    print()
    print("💡 使用技巧：")
    print("- 拖拽文件/文件夹到终端可自动输入路径")
    print("- 失败的图片可以手动调整参数重新处理")
    print("- 建议先用小批量测试效果")

if __name__ == '__main__':
    main()
