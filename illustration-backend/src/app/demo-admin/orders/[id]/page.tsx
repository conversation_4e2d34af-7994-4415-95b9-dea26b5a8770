'use client'

import { useState, useEffect } from 'react'
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { formatDate } from '@/lib/utils'
import { ArrowLeft, Download, Edit, Mail } from 'lucide-react'
import Image from 'next/image'

// 演示订单数据
const demoOrdersDetail: { [key: string]: any } = {
  'ORD-20241201-ABC123': {
    id: 'demo-1',
    order_id: 'ORD-20241201-ABC123',
    service_type: 'brand',
    service_name: '品牌插画',
    price: 5,
    customer_contact: '<EMAIL>',
    requirements: '需要一个现代简约风格的品牌插画，主要用于网站首页展示。希望能体现科技感和专业性。\n\n具体要求：\n1. 色彩以蓝色和白色为主\n2. 风格简约现代\n3. 尺寸：1920x1080px\n4. 格式：PNG和SVG\n5. 交付时间：7个工作日内',
    photos: [
      'https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=400&h=300&fit=crop',
      'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&h=300&fit=crop'
    ],
    style_images: [
      'https://images.unsplash.com/photo-1541961017774-22349e4a1262?w=400&h=300&fit=crop'
    ],
    paypal_transaction_id: 'PAYPAL-TXN-123456',
    payment_status: 'completed',
    order_status: 'in_progress',
    created_at: '2024-12-01T10:30:00Z',
    updated_at: '2024-12-01T14:20:00Z'
  },
  'ORD-20241201-DEF456': {
    id: 'demo-2',
    order_id: 'ORD-20241201-DEF456',
    service_type: 'character',
    service_name: '角色设计',
    price: 7,
    customer_contact: '<EMAIL>',
    requirements: '设计一个可爱的卡通角色，用于儿童教育应用。希望角色活泼可爱，有亲和力。\n\n角色特征：\n- 年龄：看起来像8-10岁的小朋友\n- 性格：活泼、好奇、友善\n- 服装：现代休闲装\n- 表情：微笑、眼睛明亮\n- 用途：APP图标和界面装饰',
    photos: [
      'https://images.unsplash.com/photo-1494790108755-2616c9c0e8e0?w=400&h=300&fit=crop'
    ],
    style_images: [
      'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400&h=300&fit=crop',
      'https://images.unsplash.com/photo-1513475382585-d06e58bcb0e0?w=400&h=300&fit=crop'
    ],
    paypal_transaction_id: null,
    payment_status: 'pending',
    order_status: 'pending',
    created_at: '2024-12-01T09:15:00Z',
    updated_at: '2024-12-01T09:15:00Z'
  },
  'ORD-20241130-GHI789': {
    id: 'demo-3',
    order_id: 'ORD-20241130-GHI789',
    service_type: 'scene',
    service_name: '场景插画',
    price: 10,
    customer_contact: '<EMAIL>',
    requirements: '创作一幅梦幻森林场景插画，用于小说封面。需要神秘而美丽的氛围。\n\n场景描述：\n- 时间：黄昏时分\n- 环境：古老的森林深处\n- 光线：透过树叶的金色阳光\n- 元素：古树、藤蔓、萤火虫\n- 氛围：神秘、梦幻、宁静\n- 尺寸：封面比例 6:9',
    photos: [],
    style_images: [
      'https://images.unsplash.com/photo-1596548438137-d51ea5c83ca4?w=400&h=300&fit=crop'
    ],
    paypal_transaction_id: 'PAYPAL-TXN-789012',
    payment_status: 'completed',
    order_status: 'completed',
    created_at: '2024-11-30T16:45:00Z',
    updated_at: '2024-12-01T12:00:00Z'
  }
}

export default function DemoOrderDetailPage() {
  const params = useParams()
  const router = useRouter()
  const [order, setOrder] = useState<any>(null)

  useEffect(() => {
    const orderData = demoOrdersDetail[params.id as string]
    setOrder(orderData || null)
  }, [params.id])

  const getStatusBadge = (status: string, type: 'order' | 'payment') => {
    const variants = {
      order: {
        pending: 'warning',
        in_progress: 'default',
        completed: 'success',
        cancelled: 'destructive'
      },
      payment: {
        pending: 'warning',
        completed: 'success',
        failed: 'destructive',
        refunded: 'secondary'
      }
    }

    return (
      <Badge variant={variants[type][status as keyof typeof variants[typeof type]] as any}>
        {status.replace('_', ' ').toUpperCase()}
      </Badge>
    )
  }

  const getServiceIcon = (serviceType: string) => {
    const icons = {
      brand: '🎨',
      character: '👤',
      scene: '🖼️'
    }
    return icons[serviceType as keyof typeof icons] || '📋'
  }

  if (!order) {
    return (
      <div className="container mx-auto p-6">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-4">订单未找到</h1>
          <Button onClick={() => router.back()}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            返回
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div className="flex items-center gap-4">
          <Button variant="outline" onClick={() => router.back()}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            返回
          </Button>
          <div>
            <h1 className="text-3xl font-bold">订单详情</h1>
            <p className="text-muted-foreground">订单ID: {order.order_id}</p>
          </div>
        </div>
        <div className="flex gap-2">
          <Button variant="outline">
            <Mail className="h-4 w-4 mr-2" />
            发送邮件
          </Button>
          <Button variant="outline">
            <Edit className="h-4 w-4 mr-2" />
            编辑状态
          </Button>
        </div>
      </div>

      {/* 演示提示 */}
      <div className="bg-green-50 border border-green-200 rounded-lg p-4">
        <div className="flex items-center">
          <div className="text-green-600 mr-3">✨</div>
          <div>
            <h3 className="font-semibold text-green-800">演示订单详情</h3>
            <p className="text-green-700 text-sm">
              这是订单详情页面的演示，展示了完整的订单信息、文件预览和管理功能。
            </p>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* 订单信息 */}
        <div className="lg:col-span-2 space-y-6">
          {/* 基本信息 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <span>{getServiceIcon(order.service_type)}</span>
                订单信息
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-muted-foreground">服务类型</label>
                  <p className="font-semibold">{order.service_name}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">价格</label>
                  <p className="font-semibold text-lg">${order.price}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">客户联系方式</label>
                  <p>{order.customer_contact}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">创建时间</label>
                  <p>{formatDate(order.created_at)}</p>
                </div>
              </div>
              
              {order.paypal_transaction_id && (
                <div>
                  <label className="text-sm font-medium text-muted-foreground">PayPal 交易ID</label>
                  <p className="font-mono text-sm">{order.paypal_transaction_id}</p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* 需求描述 */}
          <Card>
            <CardHeader>
              <CardTitle>客户需求</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="whitespace-pre-wrap bg-muted p-4 rounded-md">
                {order.requirements}
              </div>
            </CardContent>
          </Card>

          {/* 客户照片 */}
          {order.photos && order.photos.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>客户照片 ({order.photos.length})</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                  {order.photos.map((photo: string, index: number) => (
                    <div key={index} className="relative aspect-square rounded-lg overflow-hidden border">
                      <Image
                        src={photo}
                        alt={`客户照片 ${index + 1}`}
                        fill
                        className="object-cover hover:scale-105 transition-transform cursor-pointer"
                        onClick={() => window.open(photo, '_blank')}
                      />
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* 风格参考图 */}
          {order.style_images && order.style_images.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>风格参考图 ({order.style_images.length})</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                  {order.style_images.map((image: string, index: number) => (
                    <div key={index} className="relative aspect-square rounded-lg overflow-hidden border">
                      <Image
                        src={image}
                        alt={`风格参考图 ${index + 1}`}
                        fill
                        className="object-cover hover:scale-105 transition-transform cursor-pointer"
                        onClick={() => window.open(image, '_blank')}
                      />
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
        </div>

        {/* 侧边栏 */}
        <div className="space-y-6">
          {/* 状态信息 */}
          <Card>
            <CardHeader>
              <CardTitle>状态信息</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <label className="text-sm font-medium text-muted-foreground">订单状态</label>
                <div className="mt-1">
                  {getStatusBadge(order.order_status, 'order')}
                </div>
              </div>
              <div>
                <label className="text-sm font-medium text-muted-foreground">支付状态</label>
                <div className="mt-1">
                  {getStatusBadge(order.payment_status, 'payment')}
                </div>
              </div>
              <div>
                <label className="text-sm font-medium text-muted-foreground">最后更新</label>
                <p className="text-sm">{formatDate(order.updated_at)}</p>
              </div>
            </CardContent>
          </Card>

          {/* 操作按钮 */}
          <Card>
            <CardHeader>
              <CardTitle>操作</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <Button className="w-full" variant="outline">
                <Download className="h-4 w-4 mr-2" />
                下载所有文件
              </Button>
              <Button className="w-full" variant="outline">
                <Mail className="h-4 w-4 mr-2" />
                发送邮件通知
              </Button>
              <Button className="w-full" variant="outline">
                更新订单状态
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
