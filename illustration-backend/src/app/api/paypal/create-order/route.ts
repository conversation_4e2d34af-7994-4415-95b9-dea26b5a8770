import { NextRequest, NextResponse } from 'next/server'
import { createPayPalOrder } from '@/lib/paypal'

// 创建 PayPal 支付订单（未来使用）
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { orderId, amount, currency = 'USD', description } = body

    if (!orderId || !amount || !description) {
      return NextResponse.json(
        { success: false, error: 'Missing required fields' },
        { status: 400 }
      )
    }

    const baseUrl = process.env.NEXTAUTH_URL || 'http://localhost:3000'
    
    const paypalOrder = await createPayPalOrder({
      orderId,
      amount,
      currency,
      description,
      returnUrl: `${baseUrl}/payment/success?order_id=${orderId}`,
      cancelUrl: `${baseUrl}/payment/cancel?order_id=${orderId}`
    })

    return NextResponse.json({
      success: true,
      data: {
        paypal_order_id: paypalOrder.id,
        approval_url: paypalOrder.links.find(link => link.rel === 'approve')?.href
      }
    })
  } catch (error) {
    console.error('Create PayPal order error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to create PayPal order' },
      { status: 500 }
    )
  }
}
