import { NextRequest, NextResponse } from 'next/server'
import { supabaseAdmin, TABLES } from '@/lib/supabase'

// PayPal Webhook 事件处理
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { event_type, resource } = body

    console.log('PayPal Webhook received:', { event_type, resource })

    // 验证 webhook 签名（生产环境中必须实现）
    // const isValidSignature = await verifyPayPalWebhookSignature(request, body)
    // if (!isValidSignature) {
    //   return NextResponse.json({ error: 'Invalid signature' }, { status: 401 })
    // }

    switch (event_type) {
      case 'PAYMENT.CAPTURE.COMPLETED':
        await handlePaymentCompleted(resource)
        break
      
      case 'PAYMENT.CAPTURE.DENIED':
        await handlePaymentDenied(resource)
        break
      
      case 'PAYMENT.CAPTURE.REFUNDED':
        await handlePaymentRefunded(resource)
        break
      
      default:
        console.log('Unhandled PayPal event:', event_type)
    }

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('PayPal webhook error:', error)
    return NextResponse.json(
      { success: false, error: 'Webhook processing failed' },
      { status: 500 }
    )
  }
}

// 处理支付完成
async function handlePaymentCompleted(resource: any) {
  try {
    const transactionId = resource.id
    const customId = resource.custom_id // 这里应该包含我们的订单ID
    
    if (!customId) {
      console.error('No custom_id found in PayPal payment')
      return
    }

    // 更新订单状态
    const { error } = await supabaseAdmin
      .from(TABLES.ORDERS)
      .update({
        paypal_transaction_id: transactionId,
        payment_status: 'completed',
        order_status: 'in_progress' // 支付完成后，订单状态变为进行中
      })
      .eq('order_id', customId)

    if (error) {
      console.error('Failed to update order after payment:', error)
    } else {
      console.log(`Order ${customId} payment completed with transaction ${transactionId}`)
      
      // 这里可以添加发送确认邮件的逻辑
      // await sendPaymentConfirmationEmail(customId)
    }
  } catch (error) {
    console.error('Error handling payment completed:', error)
  }
}

// 处理支付被拒绝
async function handlePaymentDenied(resource: any) {
  try {
    const customId = resource.custom_id
    
    if (!customId) {
      console.error('No custom_id found in PayPal payment denial')
      return
    }

    // 更新订单状态
    const { error } = await supabaseAdmin
      .from(TABLES.ORDERS)
      .update({
        payment_status: 'failed'
      })
      .eq('order_id', customId)

    if (error) {
      console.error('Failed to update order after payment denial:', error)
    } else {
      console.log(`Order ${customId} payment denied`)
      
      // 这里可以添加发送支付失败通知邮件的逻辑
      // await sendPaymentFailedEmail(customId)
    }
  } catch (error) {
    console.error('Error handling payment denied:', error)
  }
}

// 处理退款
async function handlePaymentRefunded(resource: any) {
  try {
    const transactionId = resource.id
    const customId = resource.custom_id
    
    if (!customId) {
      console.error('No custom_id found in PayPal refund')
      return
    }

    // 更新订单状态
    const { error } = await supabaseAdmin
      .from(TABLES.ORDERS)
      .update({
        payment_status: 'refunded',
        order_status: 'cancelled'
      })
      .eq('order_id', customId)

    if (error) {
      console.error('Failed to update order after refund:', error)
    } else {
      console.log(`Order ${customId} refunded with transaction ${transactionId}`)
      
      // 这里可以添加发送退款确认邮件的逻辑
      // await sendRefundConfirmationEmail(customId)
    }
  } catch (error) {
    console.error('Error handling payment refunded:', error)
  }
}

// 验证 PayPal Webhook 签名（生产环境必须实现）
async function verifyPayPalWebhookSignature(request: NextRequest, body: any): Promise<boolean> {
  // 这里需要实现 PayPal webhook 签名验证
  // 参考: https://developer.paypal.com/docs/api/webhooks/v1/#verify-webhook-signature
  
  const webhookId = process.env.PAYPAL_WEBHOOK_ID
  const authAlgo = request.headers.get('PAYPAL-AUTH-ALGO')
  const transmission_id = request.headers.get('PAYPAL-TRANSMISSION-ID')
  const cert_id = request.headers.get('PAYPAL-CERT-ID')
  const transmission_sig = request.headers.get('PAYPAL-TRANSMISSION-SIG')
  const transmission_time = request.headers.get('PAYPAL-TRANSMISSION-TIME')
  
  if (!webhookId || !authAlgo || !transmission_id || !cert_id || !transmission_sig || !transmission_time) {
    return false
  }

  // 实际的签名验证逻辑需要调用 PayPal API
  // 这里暂时返回 true，生产环境中必须实现真正的验证
  console.warn('PayPal webhook signature verification not implemented')
  return true
}
