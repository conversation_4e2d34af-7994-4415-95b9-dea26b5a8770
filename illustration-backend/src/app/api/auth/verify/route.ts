import { NextRequest, NextResponse } from 'next/server'
import { verifyToken } from '@/lib/auth'
import jwt from 'jsonwebtoken'

export async function GET(request: NextRequest) {
  try {
    const token = request.cookies.get('admin-token')?.value

    if (!token) {
      return NextResponse.json(
        { success: false, error: 'No token provided' },
        { status: 401 }
      )
    }

    // 先尝试演示模式验证
    try {
      const JWT_SECRET = process.env.JWT_SECRET || 'demo_jwt_secret_key_for_development_only'
      const decoded = jwt.verify(token, JWT_SECRET) as any

      if (decoded.userId === 'demo-admin-id') {
        return NextResponse.json({
          success: true,
          user: {
            id: 'demo-admin-id',
            email: '<EMAIL>',
            name: 'Demo Administrator',
            role: 'admin'
          }
        })
      }
    } catch (demoError) {
      // 演示模式验证失败，继续尝试真实验证
    }

    // 尝试真实的数据库验证
    const result = await verifyToken(token)

    if (!result.success) {
      return NextResponse.json(
        { success: false, error: result.error },
        { status: 401 }
      )
    }

    return NextResponse.json({
      success: true,
      user: result.user
    })
  } catch (error) {
    console.error('Verify API error:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}
