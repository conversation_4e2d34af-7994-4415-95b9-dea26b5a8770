import { NextRequest, NextResponse } from 'next/server'
import { authenticateAdmin } from '@/lib/auth'
import jwt from 'jsonwebtoken'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { email, password } = body

    if (!email || !password) {
      return NextResponse.json(
        { success: false, error: 'Email and password are required' },
        { status: 400 }
      )
    }

    // 演示模式：允许使用演示账户登录
    if (email === '<EMAIL>' && password === 'admin123') {
      // 生成演示用的JWT token
      const JWT_SECRET = process.env.JWT_SECRET || 'demo_jwt_secret_key_for_development_only'
      const token = jwt.sign(
        {
          userId: 'demo-admin-id',
          email: '<EMAIL>',
          role: 'admin'
        },
        JWT_SECRET,
        { expiresIn: '24h' }
      )

      const response = NextResponse.json({
        success: true,
        user: {
          id: 'demo-admin-id',
          email: '<EMAIL>',
          name: 'Demo Administrator',
          role: 'admin'
        }
      })

      response.cookies.set('admin-token', token, {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'strict',
        maxAge: 24 * 60 * 60 // 24 hours
      })

      return response
    }

    // 尝试真实的数据库认证
    const result = await authenticateAdmin({ email, password })

    if (!result.success) {
      return NextResponse.json(
        { success: false, error: result.error },
        { status: 401 }
      )
    }

    // 设置HTTP-only cookie
    const response = NextResponse.json({
      success: true,
      user: result.user
    })

    response.cookies.set('admin-token', result.token!, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      maxAge: 24 * 60 * 60 // 24 hours
    })

    return response
  } catch (error) {
    console.error('Login API error:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}
