import { NextRequest, NextResponse } from 'next/server'
import { uploadFiles } from '@/lib/file-upload'
import { STORAGE_BUCKETS } from '@/lib/supabase'

// 通用文件上传接口
export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData()
    const files = formData.getAll('files') as File[]
    const bucket = formData.get('bucket') as string
    const folder = formData.get('folder') as string || ''

    if (!files || files.length === 0) {
      return NextResponse.json(
        { success: false, error: 'No files provided' },
        { status: 400 }
      )
    }

    if (!bucket) {
      return NextResponse.json(
        { success: false, error: 'Bucket name is required' },
        { status: 400 }
      )
    }

    // 验证存储桶名称
    const validBuckets = Object.values(STORAGE_BUCKETS)
    if (!validBuckets.includes(bucket as any)) {
      return NextResponse.json(
        { success: false, error: 'Invalid bucket name' },
        { status: 400 }
      )
    }

    const result = await uploadFiles(files, bucket, folder)

    if (!result.success) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Some files failed to upload',
          details: result.errors
        },
        { status: 400 }
      )
    }

    return NextResponse.json({
      success: true,
      data: {
        urls: result.urls,
        count: result.urls.length
      }
    })

  } catch (error) {
    console.error('Upload API error:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}
