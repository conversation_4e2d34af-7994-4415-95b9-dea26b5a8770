import { NextRequest, NextResponse } from 'next/server'
import { supabaseAdmin, TABLES } from '@/lib/supabase'

// 获取统计数据
export async function GET(request: NextRequest) {
  try {
    // 演示模式：返回模拟统计数据
    const demoStats = {
      overview: {
        totalOrders: 20,
        pendingOrders: 5,
        inProgressOrders: 8,
        completedOrders: 7,
        totalRevenue: 140,
        averageOrderValue: 7,
        completionRate: 0.35
      },
      monthlyData: [
        { month: '2024-01', orders: 3, revenue: 21 },
        { month: '2024-02', orders: 5, revenue: 35 },
        { month: '2024-03', orders: 4, revenue: 28 },
        { month: '2024-04', orders: 8, revenue: 56 },
        { month: '2024-05', orders: 0, revenue: 0 }
      ],
      serviceTypeStats: [
        { type: 'brand', name: '品牌插画', count: 8, revenue: 40 },
        { type: 'character', name: '人物插画', count: 7, revenue: 49 },
        { type: 'scene', name: '场景插画', count: 5, revenue: 51 }
      ],
      statusDistribution: [
        { status: 'pending', count: 5, percentage: 25 },
        { status: 'in_progress', count: 8, percentage: 40 },
        { status: 'completed', count: 7, percentage: 35 }
      ],
      recentTrends: {
        ordersGrowth: 15.2, // 相比上月增长百分比
        revenueGrowth: 8.7,
        averageCompletionTime: 5.2 // 天数
      }
    }

    // 在真实环境中，这里会查询数据库
    /*
    // 获取基础统计
    const { data: orders, error: ordersError } = await supabaseAdmin
      .from(TABLES.ORDERS)
      .select('*')
      .is('deleted_at', null)

    if (ordersError) {
      throw ordersError
    }

    // 计算统计数据
    const totalOrders = orders.length
    const pendingOrders = orders.filter(o => o.order_status === 'pending').length
    const inProgressOrders = orders.filter(o => o.order_status === 'in_progress').length
    const completedOrders = orders.filter(o => o.order_status === 'completed').length
    const totalRevenue = orders
      .filter(o => o.payment_status === 'completed')
      .reduce((sum, o) => sum + o.price, 0)
    
    // 按月统计
    const monthlyStats = {}
    orders.forEach(order => {
      const month = order.created_at.substring(0, 7) // YYYY-MM
      if (!monthlyStats[month]) {
        monthlyStats[month] = { orders: 0, revenue: 0 }
      }
      monthlyStats[month].orders++
      if (order.payment_status === 'completed') {
        monthlyStats[month].revenue += order.price
      }
    })

    // 按服务类型统计
    const serviceStats = {}
    orders.forEach(order => {
      if (!serviceStats[order.service_type]) {
        serviceStats[order.service_type] = { count: 0, revenue: 0 }
      }
      serviceStats[order.service_type].count++
      if (order.payment_status === 'completed') {
        serviceStats[order.service_type].revenue += order.price
      }
    })
    */

    return NextResponse.json({
      success: true,
      data: demoStats
    })

  } catch (error) {
    console.error('Statistics API error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to fetch statistics' },
      { status: 500 }
    )
  }
}

// 获取特定时间范围的统计数据
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { startDate, endDate, groupBy = 'day' } = body

    // 演示模式：返回模拟的时间范围统计
    const demoTimeRangeStats = {
      period: { startDate, endDate },
      summary: {
        totalOrders: 12,
        totalRevenue: 84,
        averageOrderValue: 7,
        completionRate: 0.42
      },
      timeline: [
        { date: '2024-04-01', orders: 2, revenue: 14 },
        { date: '2024-04-02', orders: 1, revenue: 7 },
        { date: '2024-04-03', orders: 3, revenue: 21 },
        { date: '2024-04-04', orders: 0, revenue: 0 },
        { date: '2024-04-05', orders: 2, revenue: 14 },
        { date: '2024-04-06', orders: 1, revenue: 7 },
        { date: '2024-04-07', orders: 3, revenue: 21 }
      ]
    }

    return NextResponse.json({
      success: true,
      data: demoTimeRangeStats
    })

  } catch (error) {
    console.error('Time range statistics API error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to fetch time range statistics' },
      { status: 500 }
    )
  }
}
