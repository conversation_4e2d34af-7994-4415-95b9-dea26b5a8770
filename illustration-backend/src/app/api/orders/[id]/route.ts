import { NextRequest, NextResponse } from 'next/server'
import { supabaseAdmin, TABLES } from '@/lib/supabase'

// 获取单个订单详情
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // 演示模式：返回模拟数据
    const demoOrders: { [key: string]: any } = {
      'ORD-20241201-ABC123': {
        id: 'demo-1',
        order_id: 'ORD-20241201-ABC123',
        service_type: 'brand',
        service_name: '品牌插画',
        price: 5,
        customer_contact: '<EMAIL>',
        requirements: '需要一个现代简约风格的品牌插画，主要用于网站首页展示。希望能体现科技感和专业性。\n\n具体要求：\n1. 色彩以蓝色和白色为主\n2. 风格简约现代\n3. 尺寸：1920x1080px\n4. 格式：PNG和SVG\n5. 交付时间：7个工作日内',
        photos: [
          'https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=400&h=300&fit=crop',
          'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&h=300&fit=crop'
        ],
        style_images: [
          'https://images.unsplash.com/photo-1541961017774-22349e4a1262?w=400&h=300&fit=crop'
        ],
        paypal_transaction_id: 'PAYPAL-TXN-123456',
        payment_status: 'completed',
        order_status: 'in_progress',
        created_at: '2024-12-01T10:30:00Z',
        updated_at: '2024-12-01T14:20:00Z'
      },
      'ORD-20241201-DEF456': {
        id: 'demo-2',
        order_id: 'ORD-20241201-DEF456',
        service_type: 'character',
        service_name: '角色设计',
        price: 7,
        customer_contact: '<EMAIL>',
        requirements: '设计一个可爱的卡通角色，用于儿童教育应用。希望角色活泼可爱，有亲和力。\n\n角色特征：\n- 年龄：看起来像8-10岁的小朋友\n- 性格：活泼、好奇、友善\n- 服装：现代休闲装\n- 表情：微笑、眼睛明亮\n- 用途：APP图标和界面装饰',
        photos: [
          'https://images.unsplash.com/photo-1494790108755-2616c9c0e8e0?w=400&h=300&fit=crop'
        ],
        style_images: [
          'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400&h=300&fit=crop',
          'https://images.unsplash.com/photo-1513475382585-d06e58bcb0e0?w=400&h=300&fit=crop'
        ],
        paypal_transaction_id: null,
        payment_status: 'pending',
        order_status: 'pending',
        created_at: '2024-12-01T09:15:00Z',
        updated_at: '2024-12-01T09:15:00Z'
      },
      'ORD-20241130-GHI789': {
        id: 'demo-3',
        order_id: 'ORD-20241130-GHI789',
        service_type: 'scene',
        service_name: '场景插画',
        price: 10,
        customer_contact: '<EMAIL>',
        requirements: '创作一幅梦幻森林场景插画，用于小说封面。需要神秘而美丽的氛围。\n\n场景描述：\n- 时间：黄昏时分\n- 环境：古老的森林深处\n- 光线：透过树叶的金色阳光\n- 元素：古树、藤蔓、萤火虫\n- 氛围：神秘、梦幻、宁静\n- 尺寸：封面比例 6:9',
        photos: [],
        style_images: [
          'https://images.unsplash.com/photo-1596548438137-d51ea5c83ca4?w=400&h=300&fit=crop'
        ],
        paypal_transaction_id: 'PAYPAL-TXN-789012',
        payment_status: 'completed',
        order_status: 'completed',
        created_at: '2024-11-30T16:45:00Z',
        updated_at: '2024-12-01T12:00:00Z'
      }
    }

    const order = demoOrders[params.id]

    if (!order) {
      return NextResponse.json(
        { success: false, error: 'Order not found' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      data: order
    })

    // 真实数据库查询代码（当配置了 Supabase 时使用）
    /*
    const { data: order, error } = await supabaseAdmin
      .from(TABLES.ORDERS)
      .select('*')
      .eq('order_id', params.id)
      .single()

    if (error) {
      if (error.code === 'PGRST116') {
        return NextResponse.json(
          { success: false, error: 'Order not found' },
          { status: 404 }
        )
      }
      console.error('Database error:', error)
      return NextResponse.json(
        { success: false, error: 'Failed to fetch order' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      data: order
    })
    */
  } catch (error) {
    console.error('API error:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// 更新订单状态
export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const body = await request.json()
    const { order_status, payment_status, paypal_transaction_id } = body

    const updateData: any = {}
    
    if (order_status) updateData.order_status = order_status
    if (payment_status) updateData.payment_status = payment_status
    if (paypal_transaction_id) updateData.paypal_transaction_id = paypal_transaction_id

    if (Object.keys(updateData).length === 0) {
      return NextResponse.json(
        { success: false, error: 'No valid fields to update' },
        { status: 400 }
      )
    }

    const { data: order, error } = await supabaseAdmin
      .from(TABLES.ORDERS)
      .update(updateData)
      .eq('order_id', params.id)
      .select()
      .single()

    if (error) {
      console.error('Database error:', error)
      return NextResponse.json(
        { success: false, error: 'Failed to update order' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      data: order
    })
  } catch (error) {
    console.error('API error:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// 删除订单
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { error } = await supabaseAdmin
      .from(TABLES.ORDERS)
      .delete()
      .eq('order_id', params.id)

    if (error) {
      console.error('Database error:', error)
      return NextResponse.json(
        { success: false, error: 'Failed to delete order' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      message: 'Order deleted successfully'
    })
  } catch (error) {
    console.error('API error:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}
