import { NextRequest, NextResponse } from 'next/server'
import { supabaseAdmin, TABLES, STORAGE_BUCKETS } from '@/lib/supabase'
import { generateOrderId, generateUUID, isValidImageFile, isValidFileSize } from '@/lib/utils'
import { Order } from '@/types/order'

// 获取订单列表
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const status = searchParams.get('status')
    const serviceType = searchParams.get('service_type')
    const search = searchParams.get('search')

    // 演示模式：返回模拟数据
    const demoOrders = [
      {
        id: 'demo-1',
        order_id: 'ORD-20241201-ABC123',
        service_type: 'brand',
        service_name: '品牌插画',
        price: 5,
        customer_contact: '<EMAIL>',
        requirements: '需要一个现代简约风格的品牌插画，主要用于网站首页展示。希望能体现科技感和专业性。',
        photos: [
          'https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=300&h=200&fit=crop',
          'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=300&h=200&fit=crop'
        ],
        style_images: [
          'https://images.unsplash.com/photo-1541961017774-22349e4a1262?w=300&h=200&fit=crop'
        ],
        paypal_transaction_id: 'PAYPAL-TXN-123456',
        payment_status: 'completed',
        order_status: 'in_progress',
        created_at: '2024-12-01T10:30:00Z',
        updated_at: '2024-12-01T14:20:00Z'
      },
      {
        id: 'demo-2',
        order_id: 'ORD-20241201-DEF456',
        service_type: 'character',
        service_name: '角色设计',
        price: 7,
        customer_contact: '<EMAIL>',
        requirements: '设计一个可爱的卡通角色，用于儿童教育应用。希望角色活泼可爱，有亲和力。',
        photos: [
          'https://images.unsplash.com/photo-1494790108755-2616c9c0e8e0?w=300&h=200&fit=crop'
        ],
        style_images: [
          'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=300&h=200&fit=crop',
          'https://images.unsplash.com/photo-1513475382585-d06e58bcb0e0?w=300&h=200&fit=crop'
        ],
        paypal_transaction_id: null,
        payment_status: 'pending',
        order_status: 'pending',
        created_at: '2024-12-01T09:15:00Z',
        updated_at: '2024-12-01T09:15:00Z'
      },
      {
        id: 'demo-3',
        order_id: 'ORD-20241130-GHI789',
        service_type: 'scene',
        service_name: '场景插画',
        price: 10,
        customer_contact: '<EMAIL>',
        requirements: '创作一幅梦幻森林场景插画，用于小说封面。需要神秘而美丽的氛围。',
        photos: [],
        style_images: [
          'https://images.unsplash.com/photo-1596548438137-d51ea5c83ca4?w=300&h=200&fit=crop'
        ],
        paypal_transaction_id: 'PAYPAL-TXN-789012',
        payment_status: 'completed',
        order_status: 'completed',
        created_at: '2024-11-30T16:45:00Z',
        updated_at: '2024-12-01T12:00:00Z'
      }
    ]

    // 应用过滤条件
    let filteredOrders = demoOrders
    if (status) {
      filteredOrders = filteredOrders.filter(order => order.order_status === status)
    }
    if (serviceType) {
      filteredOrders = filteredOrders.filter(order => order.service_type === serviceType)
    }
    if (search) {
      filteredOrders = filteredOrders.filter(order =>
        order.order_id.toLowerCase().includes(search.toLowerCase()) ||
        (order.paypal_transaction_id && order.paypal_transaction_id.toLowerCase().includes(search.toLowerCase()))
      )
    }

    // 分页
    const total = filteredOrders.length
    const from = (page - 1) * limit
    const to = from + limit
    const paginatedOrders = filteredOrders.slice(from, to)

    return NextResponse.json({
      success: true,
      data: {
        orders: paginatedOrders,
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit)
      }
    })

    // 真实数据库查询代码（当配置了 Supabase 时使用）
    /*
    let query = supabaseAdmin
      .from(TABLES.ORDERS)
      .select('*', { count: 'exact' })
      .order('created_at', { ascending: false })

    // 添加过滤条件
    if (status) {
      query = query.eq('order_status', status)
    }
    if (serviceType) {
      query = query.eq('service_type', serviceType)
    }

    // 分页
    const from = (page - 1) * limit
    const to = from + limit - 1
    query = query.range(from, to)

    const { data: orders, error, count } = await query

    if (error) {
      console.error('Database error:', error)
      return NextResponse.json(
        { success: false, error: 'Failed to fetch orders' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      data: {
        orders: orders || [],
        total: count || 0,
        page,
        limit,
        totalPages: Math.ceil((count || 0) / limit)
      }
    })
    */
  } catch (error) {
    console.error('API error:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// 创建新订单
export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData()
    
    // 提取表单数据
    const serviceType = formData.get('service_type') as string
    const serviceName = formData.get('service_name') as string
    const price = parseFloat(formData.get('price') as string)
    const customerContact = formData.get('customer_contact') as string
    const requirements = formData.get('requirements') as string
    
    // 提取文件
    const photoFiles = formData.getAll('photos') as File[]
    const styleFiles = formData.getAll('style_images') as File[]

    // 验证必填字段
    if (!serviceType || !serviceName || !price || !customerContact || !requirements) {
      return NextResponse.json(
        { success: false, error: 'Missing required fields' },
        { status: 400 }
      )
    }

    // 验证文件
    const allFiles = [...photoFiles, ...styleFiles]
    for (const file of allFiles) {
      if (file.size > 0) { // 只验证非空文件
        if (!isValidImageFile(file)) {
          return NextResponse.json(
            { success: false, error: `Invalid file type: ${file.name}` },
            { status: 400 }
          )
        }
        if (!isValidFileSize(file)) {
          return NextResponse.json(
            { success: false, error: `File too large: ${file.name}` },
            { status: 400 }
          )
        }
      }
    }

    // 生成订单ID
    const orderId = generateOrderId()

    // 上传照片
    const photoUrls: string[] = []
    for (const file of photoFiles) {
      if (file.size > 0) {
        const fileName = `${orderId}/photos/${generateUUID()}-${file.name}`
        const { data, error } = await supabaseAdmin.storage
          .from(STORAGE_BUCKETS.PHOTOS)
          .upload(fileName, file)

        if (error) {
          console.error('Photo upload error:', error)
          return NextResponse.json(
            { success: false, error: 'Failed to upload photos' },
            { status: 500 }
          )
        }

        const { data: { publicUrl } } = supabaseAdmin.storage
          .from(STORAGE_BUCKETS.PHOTOS)
          .getPublicUrl(fileName)
        
        photoUrls.push(publicUrl)
      }
    }

    // 上传风格图
    const styleUrls: string[] = []
    for (const file of styleFiles) {
      if (file.size > 0) {
        const fileName = `${orderId}/styles/${generateUUID()}-${file.name}`
        const { data, error } = await supabaseAdmin.storage
          .from(STORAGE_BUCKETS.STYLE_IMAGES)
          .upload(fileName, file)

        if (error) {
          console.error('Style image upload error:', error)
          return NextResponse.json(
            { success: false, error: 'Failed to upload style images' },
            { status: 500 }
          )
        }

        const { data: { publicUrl } } = supabaseAdmin.storage
          .from(STORAGE_BUCKETS.STYLE_IMAGES)
          .getPublicUrl(fileName)
        
        styleUrls.push(publicUrl)
      }
    }

    // 创建订单记录
    const orderData = {
      order_id: orderId,
      service_type: serviceType,
      service_name: serviceName,
      price: price,
      customer_contact: customerContact,
      requirements: requirements,
      photos: photoUrls,
      style_images: styleUrls,
      payment_status: 'pending',
      order_status: 'pending'
    }

    const { data: order, error: dbError } = await supabaseAdmin
      .from(TABLES.ORDERS)
      .insert(orderData)
      .select()
      .single()

    if (dbError) {
      console.error('Database insert error:', dbError)
      return NextResponse.json(
        { success: false, error: 'Failed to create order' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      data: {
        order_id: orderId,
        order: order
      }
    })

  } catch (error) {
    console.error('Order creation error:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}
