import { NextRequest, NextResponse } from 'next/server'
import { supabaseAdmin, TABLES } from '@/lib/supabase'

// 批量更新订单状态
export async function PATCH(request: NextRequest) {
  try {
    const body = await request.json()
    const { orderIds, status } = body

    if (!orderIds || !Array.isArray(orderIds) || orderIds.length === 0) {
      return NextResponse.json(
        { success: false, error: 'Order IDs are required' },
        { status: 400 }
      )
    }

    if (!status || !['pending', 'in_progress', 'completed', 'cancelled'].includes(status)) {
      return NextResponse.json(
        { success: false, error: 'Valid status is required' },
        { status: 400 }
      )
    }

    // 演示模式：返回成功响应
    // 在真实环境中，这里会更新数据库
    /*
    const { data, error } = await supabaseAdmin
      .from(TABLES.ORDERS)
      .update({ 
        order_status: status,
        updated_at: new Date().toISOString()
      })
      .in('id', orderIds)
      .select()

    if (error) {
      console.error('Batch update error:', error)
      return NextResponse.json(
        { success: false, error: 'Failed to update orders' },
        { status: 500 }
      )
    }
    */

    return NextResponse.json({
      success: true,
      message: `Successfully updated ${orderIds.length} orders to ${status}`,
      updatedCount: orderIds.length
    })

  } catch (error) {
    console.error('Batch update API error:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// 批量删除订单
export async function DELETE(request: NextRequest) {
  try {
    const body = await request.json()
    const { orderIds } = body

    if (!orderIds || !Array.isArray(orderIds) || orderIds.length === 0) {
      return NextResponse.json(
        { success: false, error: 'Order IDs are required' },
        { status: 400 }
      )
    }

    // 演示模式：返回成功响应
    // 在真实环境中，这里会软删除订单（添加deleted_at字段）
    /*
    const { data, error } = await supabaseAdmin
      .from(TABLES.ORDERS)
      .update({ 
        deleted_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .in('id', orderIds)
      .select()

    if (error) {
      console.error('Batch delete error:', error)
      return NextResponse.json(
        { success: false, error: 'Failed to delete orders' },
        { status: 500 }
      )
    }
    */

    return NextResponse.json({
      success: true,
      message: `Successfully deleted ${orderIds.length} orders`,
      deletedCount: orderIds.length
    })

  } catch (error) {
    console.error('Batch delete API error:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}
