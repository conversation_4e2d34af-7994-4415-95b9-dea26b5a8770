'use client'

import { useState } from 'react'

export default function TestLoginPage() {
  const [result, setResult] = useState('')

  const testLogin = async () => {
    try {
      const response = await fetch('/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: '<EMAIL>',
          password: 'admin123'
        }),
      })

      const data = await response.json()
      setResult(JSON.stringify(data, null, 2))
      
      if (data.success) {
        // 测试跳转
        setTimeout(() => {
          window.location.href = '/admin'
        }, 2000)
      }
    } catch (error) {
      setResult('Error: ' + error)
    }
  }

  return (
    <div className="p-8">
      <h1 className="text-2xl font-bold mb-4">登录测试页面</h1>
      <button 
        onClick={testLogin}
        className="bg-blue-500 text-white px-4 py-2 rounded mb-4"
      >
        测试登录
      </button>
      
      {result && (
        <div className="bg-gray-100 p-4 rounded">
          <h2 className="font-bold mb-2">结果:</h2>
          <pre>{result}</pre>
        </div>
      )}
    </div>
  )
}
