'use client'

import React, { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Order } from '@/types/order'
import { formatDate } from '@/lib/utils'
import { DollarSign, Clock, CheckCircle, Users, ShoppingCart, Calendar, BarChart3, TrendingUp, Activity, Search, Menu, X, ArrowUpDown, ArrowUp, ArrowDown, Trash2, Eye, RefreshCw, Filter } from 'lucide-react'

export default function AdminDashboard() {
  // 基础状态
  const [orders, setOrders] = useState<Order[]>([])
  const [loading, setLoading] = useState(true)
  const [stats, setStats] = useState({
    total: 0,
    pending: 0,
    completed: 0,
    revenue: 0
  })

  // 演示版本的完整状态管理
  const [selectedOrder, setSelectedOrder] = useState<any>(null)
  const [activeTab, setActiveTab] = useState('orders')
  const [searchQuery, setSearchQuery] = useState('')
  const [searchResult, setSearchResult] = useState<any>(null)
  const [sidebarOpen, setSidebarOpen] = useState(false)
  const [statusFilter, setStatusFilter] = useState<'all' | 'pending' | 'in_progress' | 'completed'>('all')
  const [priceFilter, setPriceFilter] = useState<'all' | 5 | 7 | 10>('all')
  const [sortBy, setSortBy] = useState<'created_at' | 'price'>('created_at')
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc')
  const [currentTime, setCurrentTime] = useState(new Date())
  const [currentPage, setCurrentPage] = useState(1)
  const [pageSize] = useState(10)
  const [jumpToPage, setJumpToPage] = useState('')
  const [selectedOrders, setSelectedOrders] = useState<string[]>([])
  const [showBatchActions, setShowBatchActions] = useState(false)
  const [batchPanelPosition, setBatchPanelPosition] = useState({ x: 50, y: 100 })
  const [isDragging, setIsDragging] = useState(false)
  const [dragOffset, setDragOffset] = useState({ x: 0, y: 0 })
  const [timeAdjustments, setTimeAdjustments] = useState<{[key: string]: number}>({})
  const [timeAdjustmentInput, setTimeAdjustmentInput] = useState('')
  const [deletedOrders, setDeletedOrders] = useState<any[]>([])
  const [showAddOrderForm, setShowAddOrderForm] = useState(false)
  const [nextSequenceNumber, setNextSequenceNumber] = useState(21)
  const [scrollPosition, setScrollPosition] = useState(0)
  const [editingNotes, setEditingNotes] = useState(false)
  const [tempNotes, setTempNotes] = useState('')

  // 菜单项配置
  const menuItems = [
    { id: 'orders', label: '订单', icon: ShoppingCart },
    { id: 'statistics', label: '数据统计', icon: BarChart3 },
    { id: 'search', label: '查询', icon: Search },
    { id: 'trash', label: '回收站', icon: Trash2 }
  ]

  const fetchOrders = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/orders')
      const data = await response.json()

      if (data.success) {
        setOrders(data.data.orders)

        // 计算统计数据
        const total = data.data.orders.length
        const pending = data.data.orders.filter((o: Order) => o.order_status === 'pending').length
        const completed = data.data.orders.filter((o: Order) => o.order_status === 'completed').length
        const revenue = data.data.orders
          .filter((o: Order) => o.payment_status === 'completed')
          .reduce((sum: number, o: Order) => sum + o.price, 0)

        setStats({ total, pending, completed, revenue })
      }
    } catch (error) {
      console.error('Failed to fetch orders:', error)
    } finally {
      setLoading(false)
    }
  }

  const fetchStatistics = async () => {
    try {
      const response = await fetch('/api/statistics')
      const data = await response.json()

      if (data.success) {
        // 更新统计数据
        const { overview } = data.data
        setStats({
          total: overview.totalOrders,
          pending: overview.pendingOrders,
          completed: overview.completedOrders,
          revenue: overview.totalRevenue
        })
      }
    } catch (error) {
      console.error('Failed to fetch statistics:', error)
    }
  }

  useEffect(() => {
    fetchOrders()
  }, [])

  // 时间更新定时器
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date())
    }, 60000) // 每分钟更新

    return () => clearInterval(timer)
  }, [])

  // 搜索功能
  const handleSearch = async () => {
    if (!searchQuery.trim()) {
      setSearchResult(null)
      return
    }

    try {
      // 调用API搜索订单
      const response = await fetch(`/api/orders?search=${encodeURIComponent(searchQuery)}`)
      const data = await response.json()

      if (data.success && data.data.orders.length > 0) {
        setSearchResult(data.data.orders[0])
      } else {
        setSearchResult('not_found')
      }
    } catch (error) {
      console.error('Search failed:', error)
      setSearchResult('not_found')
    }
  }

  // 排序功能
  const handleSort = (field: 'created_at' | 'price') => {
    if (sortBy === field) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')
    } else {
      setSortBy(field)
      setSortOrder('desc')
    }
  }

  // 筛选和排序订单
  const getFilteredAndSortedOrders = () => {
    let filtered = orders
    if (statusFilter !== 'all') {
      filtered = filtered.filter(order => order.order_status === statusFilter)
    }
    if (priceFilter !== 'all') {
      filtered = filtered.filter(order => order.price === priceFilter)
    }

    return [...filtered].sort((a, b) => {
      let aValue = a[sortBy]
      let bValue = b[sortBy]

      if (sortBy === 'created_at') {
        aValue = new Date(aValue).getTime()
        bValue = new Date(bValue).getTime()
      } else if (sortBy === 'price') {
        aValue = Number(aValue)
        bValue = Number(bValue)
      }

      if (sortOrder === 'asc') {
        return aValue > bValue ? 1 : -1
      } else {
        return aValue < bValue ? 1 : -1
      }
    })
  }

  // 分页功能
  const getCurrentPageOrders = () => {
    const allOrders = getFilteredAndSortedOrders()
    const startIndex = (currentPage - 1) * pageSize
    const endIndex = startIndex + pageSize
    return allOrders.slice(startIndex, endIndex)
  }

  const getTotalPages = () => {
    return Math.ceil(getFilteredAndSortedOrders().length / pageSize)
  }

  const handleJumpToPage = () => {
    const page = parseInt(jumpToPage)
    if (page >= 1 && page <= getTotalPages()) {
      setCurrentPage(page)
      setJumpToPage('')
    }
  }

  // 筛选条件改变时重置到第一页
  const handleStatusFilterChange = (status: 'all' | 'pending' | 'in_progress' | 'completed') => {
    setStatusFilter(status)
    setCurrentPage(1)
  }

  const handlePriceFilterChange = (price: 'all' | 5 | 7 | 10) => {
    setPriceFilter(price)
    setCurrentPage(1)
  }

  // 批量选择功能
  const handleSelectOrder = (orderId: string) => {
    setSelectedOrders(prev => {
      if (prev.includes(orderId)) {
        return prev.filter(id => id !== orderId)
      } else {
        return [...prev, orderId]
      }
    })
  }

  const handleSelectAll = () => {
    const filteredOrders = getFilteredAndSortedOrders()
    if (selectedOrders.length === filteredOrders.length) {
      setSelectedOrders([])
    } else {
      setSelectedOrders(filteredOrders.map(order => order.id))
    }
  }

  // 批量状态修改
  const handleBatchStatusChange = async (newStatus: 'pending' | 'in_progress' | 'completed') => {
    try {
      const response = await fetch('/api/orders/batch', {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          orderIds: selectedOrders,
          status: newStatus
        })
      })

      const data = await response.json()

      if (data.success) {
        setSelectedOrders([])
        setShowBatchActions(false)

        // 重新获取数据以确保同步
        await fetchOrders()
      } else {
        console.error('Batch update failed:', data.error)
      }
    } catch (error) {
      console.error('Batch status update failed:', error)
    }
  }

  // 批量删除
  const handleBatchDelete = async () => {
    if (!confirm(`确定要删除选中的 ${selectedOrders.length} 个订单吗？`)) {
      return
    }

    try {
      const response = await fetch('/api/orders/batch', {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          orderIds: selectedOrders
        })
      })

      const data = await response.json()

      if (data.success) {
        setSelectedOrders([])
        setShowBatchActions(false)

        // 重新获取数据以确保同步
        await fetchOrders()
      } else {
        console.error('Batch delete failed:', data.error)
      }
    } catch (error) {
      console.error('Batch delete failed:', error)
    }
  }

  const getStatusBadge = (status: string, type: 'order' | 'payment') => {
    const variants = {
      order: {
        pending: 'warning',
        in_progress: 'default',
        completed: 'success',
        cancelled: 'destructive'
      },
      payment: {
        pending: 'warning',
        completed: 'success',
        failed: 'destructive',
        refunded: 'secondary'
      }
    }

    return (
      <Badge variant={variants[type][status as keyof typeof variants[typeof type]] as any}>
        {status.replace('_', ' ').toUpperCase()}
      </Badge>
    )
  }

  const getServiceIcon = (serviceType: string) => {
    const icons = {
      brand: '🎨',
      character: '👤',
      scene: '🖼️'
    }
    return icons[serviceType as keyof typeof icons] || '📋'
  }

  // 渲染内容的函数
  const renderContent = () => {
    switch (activeTab) {
      case 'orders':
        return (
          <div>
            {/* 订单管理界面 */}
            <div className="space-y-6">
              {/* 统计卡片 */}
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">总订单数</CardTitle>
                    <span className="text-2xl">📊</span>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{stats.total}</div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">待处理</CardTitle>
                    <span className="text-2xl">⏳</span>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{stats.pending}</div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">已完成</CardTitle>
                    <span className="text-2xl">✅</span>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{stats.completed}</div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">总收入</CardTitle>
                    <span className="text-2xl">💰</span>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">${stats.revenue}</div>
                  </CardContent>
                </Card>
              </div>

              {/* 订单表格 */}
              <Card>
                <CardHeader>
                  <CardTitle>订单列表</CardTitle>
                  <CardDescription>
                    查看和管理所有订单
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>
                          <input
                            type="checkbox"
                            checked={selectedOrders.length === getCurrentPageOrders().length && getCurrentPageOrders().length > 0}
                            onChange={handleSelectAll}
                            className="w-4 h-4"
                          />
                        </TableHead>
                        <TableHead>订单ID</TableHead>
                        <TableHead>服务类型</TableHead>
                        <TableHead>价格</TableHead>
                        <TableHead>客户联系</TableHead>
                        <TableHead>订单状态</TableHead>
                        <TableHead>支付状态</TableHead>
                        <TableHead>创建时间</TableHead>
                        <TableHead>操作</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {getCurrentPageOrders().map((order) => (
                        <TableRow key={order.id}>
                          <TableCell>
                            <input
                              type="checkbox"
                              checked={selectedOrders.includes(order.id)}
                              onChange={() => handleSelectOrder(order.id)}
                              className="w-4 h-4"
                            />
                          </TableCell>
                          <TableCell className="font-mono text-sm">
                            {order.order_id}
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center gap-2">
                              <span>{getServiceIcon(order.service_type)}</span>
                              {order.service_name}
                            </div>
                          </TableCell>
                          <TableCell className="font-semibold">
                            ${order.price}
                          </TableCell>
                          <TableCell>{order.customer_contact}</TableCell>
                          <TableCell>
                            {getStatusBadge(order.order_status, 'order')}
                          </TableCell>
                          <TableCell>
                            {getStatusBadge(order.payment_status, 'payment')}
                          </TableCell>
                          <TableCell className="text-sm text-muted-foreground">
                            {formatDate(order.created_at)}
                          </TableCell>
                          <TableCell>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => setSelectedOrder(order)}
                            >
                              <Eye className="h-4 w-4" />
                            </Button>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>

                  {orders.length === 0 && (
                    <div className="text-center py-8 text-muted-foreground">
                      暂无订单数据
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>
          </div>
        )

      case 'statistics':
        return (
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <h2 className="text-2xl font-bold">📊 数据统计</h2>
              <Button onClick={fetchStatistics} variant="outline" size="sm">
                <RefreshCw className="h-4 w-4 mr-2" />
                刷新数据
              </Button>
            </div>

            {/* 概览统计卡片 */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">总订单数</CardTitle>
                  <ShoppingCart className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{stats.total}</div>
                  <p className="text-xs text-muted-foreground">
                    +15.2% 相比上月
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">总收入</CardTitle>
                  <DollarSign className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">${stats.revenue}</div>
                  <p className="text-xs text-muted-foreground">
                    +8.7% 相比上月
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">完成率</CardTitle>
                  <CheckCircle className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    {Math.round((stats.completed / stats.total) * 100)}%
                  </div>
                  <p className="text-xs text-muted-foreground">
                    平均完成时间 5.2 天
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">平均订单价值</CardTitle>
                  <TrendingUp className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    ${stats.total > 0 ? (stats.revenue / stats.total).toFixed(1) : '0'}
                  </div>
                  <p className="text-xs text-muted-foreground">
                    每个订单平均价值
                  </p>
                </CardContent>
              </Card>
            </div>

            {/* 服务类型分布 */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>服务类型分布</CardTitle>
                  <CardDescription>各类型订单数量统计</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <span>🎨</span>
                        <span>品牌插画</span>
                      </div>
                      <div className="text-right">
                        <div className="font-semibold">8 单</div>
                        <div className="text-sm text-muted-foreground">$40</div>
                      </div>
                    </div>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <span>👤</span>
                        <span>人物插画</span>
                      </div>
                      <div className="text-right">
                        <div className="font-semibold">7 单</div>
                        <div className="text-sm text-muted-foreground">$49</div>
                      </div>
                    </div>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <span>🖼️</span>
                        <span>场景插画</span>
                      </div>
                      <div className="text-right">
                        <div className="font-semibold">5 单</div>
                        <div className="text-sm text-muted-foreground">$51</div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>订单状态分布</CardTitle>
                  <CardDescription>当前订单状态统计</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <Badge variant="secondary">待处理</Badge>
                      </div>
                      <div className="text-right">
                        <div className="font-semibold">{stats.pending} 单</div>
                        <div className="text-sm text-muted-foreground">
                          {Math.round((stats.pending / stats.total) * 100)}%
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <Badge variant="default">制作中</Badge>
                      </div>
                      <div className="text-right">
                        <div className="font-semibold">
                          {stats.total - stats.pending - stats.completed} 单
                        </div>
                        <div className="text-sm text-muted-foreground">
                          {Math.round(((stats.total - stats.pending - stats.completed) / stats.total) * 100)}%
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <Badge variant="outline" className="border-green-500 text-green-700">已完成</Badge>
                      </div>
                      <div className="text-right">
                        <div className="font-semibold">{stats.completed} 单</div>
                        <div className="text-sm text-muted-foreground">
                          {Math.round((stats.completed / stats.total) * 100)}%
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        )

      case 'search':
        return (
          <div className="space-y-6">
            <h2 className="text-2xl font-bold">订单查询</h2>
            <div className="flex gap-4">
              <Input
                placeholder="输入订单ID或PayPal交易ID"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
              />
              <Button onClick={handleSearch}>
                <Search className="h-4 w-4 mr-2" />
                搜索
              </Button>
            </div>

            {searchResult && searchResult !== 'not_found' && (
              <Card>
                <CardHeader>
                  <CardTitle>搜索结果</CardTitle>
                </CardHeader>
                <CardContent>
                  <p><strong>订单ID:</strong> {searchResult.order_id}</p>
                  <p><strong>服务类型:</strong> {searchResult.service_name}</p>
                  <p><strong>价格:</strong> ${searchResult.price}</p>
                  <p><strong>状态:</strong> {searchResult.order_status}</p>
                </CardContent>
              </Card>
            )}

            {searchResult === 'not_found' && (
              <div className="text-center py-8 text-muted-foreground">
                未找到匹配的订单
              </div>
            )}
          </div>
        )

      case 'trash':
        return (
          <div className="space-y-6">
            <h2 className="text-2xl font-bold">回收站</h2>
            <p className="text-gray-600">回收站功能开发中...</p>
          </div>
        )

      default:
        return null
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <RefreshCw className="h-8 w-8 animate-spin" />
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 顶部导航栏 */}
      <div className="bg-white shadow-sm border-b px-6 py-4 flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setSidebarOpen(true)}
            className="lg:hidden"
          >
            <Menu className="h-5 w-5" />
          </Button>
          <div>
            <h1 className="text-xl font-bold text-gray-900">🎨 插画工作室管理系统</h1>
            <p className="text-sm text-gray-600">专业的订单管理平台</p>
          </div>
        </div>
        <div className="flex items-center space-x-3">
          <Button onClick={fetchOrders} variant="outline" size="sm">
            <RefreshCw className="h-4 w-4 mr-2" />
            刷新
          </Button>
          {selectedOrders.length > 0 && (
            <Button
              onClick={() => setShowBatchActions(!showBatchActions)}
              className="bg-blue-600 hover:bg-blue-700"
              size="sm"
            >
              批量操作 ({selectedOrders.length})
            </Button>
          )}
        </div>
      </div>

      {/* 侧边栏遮罩层 */}
      <div className={`fixed inset-0 z-40 transition-opacity duration-300 ${
        sidebarOpen ? 'opacity-100' : 'opacity-0 pointer-events-none'
      }`}>
        <div
          className="absolute inset-0 bg-black bg-opacity-50"
          onClick={() => setSidebarOpen(false)}
        />

        {/* 侧边栏 */}
        <div className={`absolute left-0 top-0 h-full w-80 bg-white shadow-2xl transform transition-all duration-300 ease-out ${
          sidebarOpen ? 'translate-x-0 opacity-100' : '-translate-x-full opacity-0'
        }`}>
          <div className="p-6 border-b flex items-center justify-between bg-gradient-to-r from-blue-50 to-indigo-50">
            <div>
              <h2 className="text-lg font-bold text-gray-900">🎨 导航菜单</h2>
              <p className="text-sm text-gray-600">选择功能模块</p>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setSidebarOpen(false)}
              className="text-gray-500 hover:text-gray-700"
            >
              <X className="h-5 w-5" />
            </Button>
          </div>

          <nav className="p-4">
            <div className="space-y-2">
              {menuItems.map((item, index) => {
                const Icon = item.icon
                return (
                  <button
                    key={item.id}
                    onClick={() => {
                      setActiveTab(item.id)
                      setTimeout(() => setSidebarOpen(false), 150)
                    }}
                    className={`w-full flex items-center space-x-4 px-4 py-4 rounded-xl text-left transition-all duration-200 transform hover:scale-105 ${
                      activeTab === item.id
                        ? 'bg-gradient-to-r from-blue-500 to-indigo-500 text-white shadow-lg'
                        : 'text-gray-700 hover:bg-gray-50 hover:shadow-md'
                    }`}
                  >
                    <div className={`p-2 rounded-lg ${
                      activeTab === item.id
                        ? 'bg-white bg-opacity-20'
                        : 'bg-gray-100'
                    }`}>
                      <Icon className="h-5 w-5" />
                    </div>
                    <div>
                      <span className="font-medium">{item.label}</span>
                      <p className={`text-xs ${
                        activeTab === item.id ? 'text-blue-100' : 'text-gray-500'
                      }`}>
                        {item.id === 'orders' && '管理所有订单'}
                        {item.id === 'statistics' && '数据分析统计'}
                        {item.id === 'search' && '快速查询订单'}
                        {item.id === 'trash' && '已删除订单'}
                      </p>
                    </div>
                  </button>
                )
              })}
            </div>
          </nav>

          <div className="absolute bottom-0 left-0 right-0 p-6 border-t bg-gray-50">
            <div className="text-center text-sm text-gray-500">
              <p>插画工作室管理系统</p>
              <p className="text-xs mt-1">v2.0.0</p>
            </div>
          </div>
        </div>
      </div>

      {/* 主内容区域 */}
      <div className="p-8">
        {renderContent()}
      </div>

      {/* 批量操作面板 */}
      {showBatchActions && selectedOrders.length > 0 && (
        <div
          className="fixed bg-white rounded-lg shadow-2xl border z-50 min-w-[280px]"
          style={{
            left: `${batchPanelPosition.x}px`,
            top: `${batchPanelPosition.y}px`,
          }}
        >
          <div className="p-3 border-b bg-gray-50 rounded-t-lg">
            <div className="flex items-center justify-between">
              <h3 className="font-semibold text-gray-900">
                批量操作 ({selectedOrders.length} 项)
              </h3>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowBatchActions(false)}
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          </div>

          <div className="p-3 space-y-3">
            <div className="grid grid-cols-2 gap-2">
              <Button
                size="sm"
                onClick={() => handleBatchStatusChange('pending')}
                className="bg-gray-500 hover:bg-gray-600 text-white w-full"
              >
                ⏳ 待处理
              </Button>
              <Button
                size="sm"
                onClick={() => handleBatchStatusChange('in_progress')}
                className="bg-blue-500 hover:bg-blue-600 text-white w-full"
              >
                🔄 制作中
              </Button>
              <Button
                size="sm"
                onClick={() => handleBatchStatusChange('completed')}
                className="bg-green-500 hover:bg-green-600 text-white w-full"
              >
                ✅ 已完成
              </Button>
              <Button
                size="sm"
                variant="destructive"
                className="w-full"
                onClick={handleBatchDelete}
              >
                🗑️ 删除
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
