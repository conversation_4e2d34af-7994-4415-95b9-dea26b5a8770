'use client'

import { useState, useEffect } from 'react'
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Order } from '@/types/order'
import { formatDate } from '@/lib/utils'
import { ArrowLeft, RefreshCw, Download, Edit } from 'lucide-react'
import Image from 'next/image'

export default function OrderDetailPage() {
  const params = useParams()
  const router = useRouter()
  const [order, setOrder] = useState<Order | null>(null)
  const [loading, setLoading] = useState(true)

  const fetchOrder = async () => {
    try {
      setLoading(true)
      const response = await fetch(`/api/orders/${params.id}`)
      const data = await response.json()
      
      if (data.success) {
        setOrder(data.data)
      } else {
        console.error('Failed to fetch order:', data.error)
      }
    } catch (error) {
      console.error('Failed to fetch order:', error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    if (params.id) {
      fetchOrder()
    }
  }, [params.id])

  const getStatusBadge = (status: string, type: 'order' | 'payment') => {
    const variants = {
      order: {
        pending: 'warning',
        in_progress: 'default',
        completed: 'success',
        cancelled: 'destructive'
      },
      payment: {
        pending: 'warning',
        completed: 'success',
        failed: 'destructive',
        refunded: 'secondary'
      }
    }

    return (
      <Badge variant={variants[type][status as keyof typeof variants[typeof type]] as any}>
        {status.replace('_', ' ').toUpperCase()}
      </Badge>
    )
  }

  const getServiceIcon = (serviceType: string) => {
    const icons = {
      brand: '🎨',
      character: '👤',
      scene: '🖼️'
    }
    return icons[serviceType as keyof typeof icons] || '📋'
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <RefreshCw className="h-8 w-8 animate-spin" />
      </div>
    )
  }

  if (!order) {
    return (
      <div className="container mx-auto p-6">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-4">订单未找到</h1>
          <Button onClick={() => router.back()}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            返回
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div className="flex items-center gap-4">
          <Button variant="outline" onClick={() => router.back()}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            返回
          </Button>
          <div>
            <h1 className="text-3xl font-bold">订单详情</h1>
            <p className="text-muted-foreground">订单ID: {order.order_id}</p>
          </div>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={fetchOrder}>
            <RefreshCw className="h-4 w-4 mr-2" />
            刷新
          </Button>
          <Button variant="outline">
            <Edit className="h-4 w-4 mr-2" />
            编辑状态
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* 订单信息 */}
        <div className="lg:col-span-2 space-y-6">
          {/* 基本信息 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <span>{getServiceIcon(order.service_type)}</span>
                订单信息
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-muted-foreground">服务类型</label>
                  <p className="font-semibold">{order.service_name}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">价格</label>
                  <p className="font-semibold text-lg">${order.price}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">客户联系方式</label>
                  <p>{order.customer_contact}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">创建时间</label>
                  <p>{formatDate(order.created_at)}</p>
                </div>
              </div>
              
              {order.paypal_transaction_id && (
                <div>
                  <label className="text-sm font-medium text-muted-foreground">PayPal 交易ID</label>
                  <p className="font-mono text-sm">{order.paypal_transaction_id}</p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* 需求描述 */}
          <Card>
            <CardHeader>
              <CardTitle>客户需求</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="whitespace-pre-wrap bg-muted p-4 rounded-md">
                {order.requirements}
              </div>
            </CardContent>
          </Card>

          {/* 客户照片 */}
          {order.photos && order.photos.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>客户照片 ({order.photos.length})</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                  {order.photos.map((photo, index) => (
                    <div key={index} className="relative aspect-square rounded-lg overflow-hidden border">
                      <Image
                        src={photo}
                        alt={`客户照片 ${index + 1}`}
                        fill
                        className="object-cover hover:scale-105 transition-transform cursor-pointer"
                        onClick={() => window.open(photo, '_blank')}
                      />
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* 风格参考图 */}
          {order.style_images && order.style_images.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>风格参考图 ({order.style_images.length})</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                  {order.style_images.map((image, index) => (
                    <div key={index} className="relative aspect-square rounded-lg overflow-hidden border">
                      <Image
                        src={image}
                        alt={`风格参考图 ${index + 1}`}
                        fill
                        className="object-cover hover:scale-105 transition-transform cursor-pointer"
                        onClick={() => window.open(image, '_blank')}
                      />
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
        </div>

        {/* 侧边栏 */}
        <div className="space-y-6">
          {/* 状态信息 */}
          <Card>
            <CardHeader>
              <CardTitle>状态信息</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <label className="text-sm font-medium text-muted-foreground">订单状态</label>
                <div className="mt-1">
                  {getStatusBadge(order.order_status, 'order')}
                </div>
              </div>
              <div>
                <label className="text-sm font-medium text-muted-foreground">支付状态</label>
                <div className="mt-1">
                  {getStatusBadge(order.payment_status, 'payment')}
                </div>
              </div>
              <div>
                <label className="text-sm font-medium text-muted-foreground">最后更新</label>
                <p className="text-sm">{formatDate(order.updated_at)}</p>
              </div>
            </CardContent>
          </Card>

          {/* 操作按钮 */}
          <Card>
            <CardHeader>
              <CardTitle>操作</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <Button className="w-full" variant="outline">
                <Download className="h-4 w-4 mr-2" />
                下载所有文件
              </Button>
              <Button className="w-full" variant="outline">
                发送邮件通知
              </Button>
              <Button className="w-full" variant="destructive">
                删除订单
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
