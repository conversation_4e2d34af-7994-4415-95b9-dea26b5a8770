'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Order } from '@/types/order'
import { formatDate } from '@/lib/utils'
import { Eye, RefreshCw, Filter } from 'lucide-react'

export default function AdminDashboard() {
  const [orders, setOrders] = useState<Order[]>([])
  const [loading, setLoading] = useState(true)
  const [stats, setStats] = useState({
    total: 0,
    pending: 0,
    completed: 0,
    revenue: 0
  })

  const fetchOrders = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/orders')
      const data = await response.json()
      
      if (data.success) {
        setOrders(data.data.orders)
        
        // 计算统计数据
        const total = data.data.orders.length
        const pending = data.data.orders.filter((o: Order) => o.order_status === 'pending').length
        const completed = data.data.orders.filter((o: Order) => o.order_status === 'completed').length
        const revenue = data.data.orders
          .filter((o: Order) => o.payment_status === 'completed')
          .reduce((sum: number, o: Order) => sum + o.price, 0)
        
        setStats({ total, pending, completed, revenue })
      }
    } catch (error) {
      console.error('Failed to fetch orders:', error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchOrders()
  }, [])

  const getStatusBadge = (status: string, type: 'order' | 'payment') => {
    const variants = {
      order: {
        pending: 'warning',
        in_progress: 'default',
        completed: 'success',
        cancelled: 'destructive'
      },
      payment: {
        pending: 'warning',
        completed: 'success',
        failed: 'destructive',
        refunded: 'secondary'
      }
    }

    return (
      <Badge variant={variants[type][status as keyof typeof variants[typeof type]] as any}>
        {status.replace('_', ' ').toUpperCase()}
      </Badge>
    )
  }

  const getServiceIcon = (serviceType: string) => {
    const icons = {
      brand: '🎨',
      character: '👤',
      scene: '🖼️'
    }
    return icons[serviceType as keyof typeof icons] || '📋'
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <RefreshCw className="h-8 w-8 animate-spin" />
      </div>
    )
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">订单管理后台</h1>
          <p className="text-muted-foreground">管理您的插画定制订单</p>
        </div>
        <Button onClick={fetchOrders} variant="outline">
          <RefreshCw className="h-4 w-4 mr-2" />
          刷新
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">总订单数</CardTitle>
            <span className="text-2xl">📊</span>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.total}</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">待处理</CardTitle>
            <span className="text-2xl">⏳</span>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.pending}</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">已完成</CardTitle>
            <span className="text-2xl">✅</span>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.completed}</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">总收入</CardTitle>
            <span className="text-2xl">💰</span>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">${stats.revenue}</div>
          </CardContent>
        </Card>
      </div>

      {/* Orders Table */}
      <Card>
        <CardHeader>
          <CardTitle>订单列表</CardTitle>
          <CardDescription>
            查看和管理所有订单
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>订单ID</TableHead>
                <TableHead>服务类型</TableHead>
                <TableHead>价格</TableHead>
                <TableHead>客户联系</TableHead>
                <TableHead>订单状态</TableHead>
                <TableHead>支付状态</TableHead>
                <TableHead>创建时间</TableHead>
                <TableHead>操作</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {orders.map((order) => (
                <TableRow key={order.id}>
                  <TableCell className="font-mono text-sm">
                    {order.order_id}
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <span>{getServiceIcon(order.service_type)}</span>
                      {order.service_name}
                    </div>
                  </TableCell>
                  <TableCell className="font-semibold">
                    ${order.price}
                  </TableCell>
                  <TableCell>{order.customer_contact}</TableCell>
                  <TableCell>
                    {getStatusBadge(order.order_status, 'order')}
                  </TableCell>
                  <TableCell>
                    {getStatusBadge(order.payment_status, 'payment')}
                  </TableCell>
                  <TableCell className="text-sm text-muted-foreground">
                    {formatDate(order.created_at)}
                  </TableCell>
                  <TableCell>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => window.location.href = `/admin/orders/${order.order_id}`}
                    >
                      <Eye className="h-4 w-4" />
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
          
          {orders.length === 0 && (
            <div className="text-center py-8 text-muted-foreground">
              暂无订单数据
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
