import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.SUPABASE_URL!
const supabaseAnonKey = process.env.SUPABASE_ANON_KEY!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!

// 客户端实例（用于前端操作）
export const supabase = createClient(supabaseUrl, supabaseAnonKey)

// 服务端实例（用于管理员操作，拥有更高权限）
export const supabaseAdmin = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
})

// 数据库表名
export const TABLES = {
  ORDERS: 'orders',
  ADMIN_USERS: 'admin_users'
} as const

// 存储桶名
export const STORAGE_BUCKETS = {
  PHOTOS: 'customer-photos',
  STYLE_IMAGES: 'style-references'
} as const
