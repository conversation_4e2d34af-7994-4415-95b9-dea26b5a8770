import { supabaseAdmin, STORAGE_BUCKETS } from './supabase'
import { generateUUID, isValidImageFile, isValidFileSize } from './utils'

export interface UploadResult {
  success: boolean
  url?: string
  error?: string
}

export interface BatchUploadResult {
  success: boolean
  urls: string[]
  errors: string[]
}

// 上传单个文件到指定存储桶
export async function uploadFile(
  file: File,
  bucket: string,
  folder: string = ''
): Promise<UploadResult> {
  try {
    // 验证文件
    if (!isValidImageFile(file)) {
      return {
        success: false,
        error: `Invalid file type: ${file.name}. Only JPEG, PNG, and WebP are allowed.`
      }
    }

    if (!isValidFileSize(file, 5)) {
      return {
        success: false,
        error: `File too large: ${file.name}. Maximum size is 5MB.`
      }
    }

    // 生成唯一文件名
    const fileExtension = file.name.split('.').pop()
    const fileName = `${folder ? folder + '/' : ''}${generateUUID()}.${fileExtension}`

    // 上传文件
    const { data, error } = await supabaseAdmin.storage
      .from(bucket)
      .upload(fileName, file, {
        cacheControl: '3600',
        upsert: false
      })

    if (error) {
      console.error('File upload error:', error)
      return {
        success: false,
        error: `Failed to upload ${file.name}: ${error.message}`
      }
    }

    // 获取公共URL
    const { data: { publicUrl } } = supabaseAdmin.storage
      .from(bucket)
      .getPublicUrl(fileName)

    return {
      success: true,
      url: publicUrl
    }
  } catch (error) {
    console.error('Upload error:', error)
    return {
      success: false,
      error: `Unexpected error uploading ${file.name}`
    }
  }
}

// 批量上传文件
export async function uploadFiles(
  files: File[],
  bucket: string,
  folder: string = ''
): Promise<BatchUploadResult> {
  const urls: string[] = []
  const errors: string[] = []

  for (const file of files) {
    if (file.size > 0) { // 只处理非空文件
      const result = await uploadFile(file, bucket, folder)
      
      if (result.success && result.url) {
        urls.push(result.url)
      } else {
        errors.push(result.error || `Failed to upload ${file.name}`)
      }
    }
  }

  return {
    success: errors.length === 0,
    urls,
    errors
  }
}

// 上传客户照片
export async function uploadCustomerPhotos(
  files: File[],
  orderId: string
): Promise<BatchUploadResult> {
  return uploadFiles(files, STORAGE_BUCKETS.PHOTOS, `${orderId}/photos`)
}

// 上传风格参考图
export async function uploadStyleImages(
  files: File[],
  orderId: string
): Promise<BatchUploadResult> {
  return uploadFiles(files, STORAGE_BUCKETS.STYLE_IMAGES, `${orderId}/styles`)
}

// 删除文件
export async function deleteFile(
  bucket: string,
  filePath: string
): Promise<{ success: boolean; error?: string }> {
  try {
    const { error } = await supabaseAdmin.storage
      .from(bucket)
      .remove([filePath])

    if (error) {
      console.error('File deletion error:', error)
      return {
        success: false,
        error: error.message
      }
    }

    return { success: true }
  } catch (error) {
    console.error('Delete error:', error)
    return {
      success: false,
      error: 'Unexpected error during file deletion'
    }
  }
}

// 从URL中提取文件路径
export function extractFilePathFromUrl(url: string, bucket: string): string | null {
  try {
    const urlObj = new URL(url)
    const pathParts = urlObj.pathname.split('/')
    const bucketIndex = pathParts.indexOf(bucket)
    
    if (bucketIndex !== -1 && bucketIndex < pathParts.length - 1) {
      return pathParts.slice(bucketIndex + 1).join('/')
    }
    
    return null
  } catch {
    return null
  }
}

// 删除订单相关的所有文件
export async function deleteOrderFiles(orderId: string): Promise<void> {
  try {
    // 删除照片
    await supabaseAdmin.storage
      .from(STORAGE_BUCKETS.PHOTOS)
      .remove([`${orderId}/`])

    // 删除风格图
    await supabaseAdmin.storage
      .from(STORAGE_BUCKETS.STYLE_IMAGES)
      .remove([`${orderId}/`])
  } catch (error) {
    console.error('Error deleting order files:', error)
  }
}
