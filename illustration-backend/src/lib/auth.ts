import bcrypt from 'bcryptjs'
import jwt from 'jsonwebtoken'
import { supabaseAdmin, TABLES } from './supabase'

const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key'

export interface AdminUser {
  id: string
  email: string
  name?: string
  role: string
}

export interface LoginCredentials {
  email: string
  password: string
}

export interface AuthResult {
  success: boolean
  user?: AdminUser
  token?: string
  error?: string
}

// 验证管理员登录
export async function authenticateAdmin(credentials: LoginCredentials): Promise<AuthResult> {
  try {
    const { email, password } = credentials

    // 从数据库获取管理员用户
    const { data: user, error } = await supabaseAdmin
      .from(TABLES.ADMIN_USERS)
      .select('*')
      .eq('email', email)
      .eq('is_active', true)
      .single()

    if (error || !user) {
      return {
        success: false,
        error: 'Invalid email or password'
      }
    }

    // 验证密码
    const isValidPassword = await bcrypt.compare(password, user.password_hash)
    if (!isValidPassword) {
      return {
        success: false,
        error: 'Invalid email or password'
      }
    }

    // 更新最后登录时间
    await supabaseAdmin
      .from(TABLES.ADMIN_USERS)
      .update({ last_login: new Date().toISOString() })
      .eq('id', user.id)

    // 生成JWT token
    const token = jwt.sign(
      {
        userId: user.id,
        email: user.email,
        role: user.role
      },
      JWT_SECRET,
      { expiresIn: '24h' }
    )

    return {
      success: true,
      user: {
        id: user.id,
        email: user.email,
        name: user.name,
        role: user.role
      },
      token
    }
  } catch (error) {
    console.error('Authentication error:', error)
    return {
      success: false,
      error: 'Authentication failed'
    }
  }
}

// 验证JWT token
export async function verifyToken(token: string): Promise<{ success: boolean; user?: AdminUser; error?: string }> {
  try {
    const decoded = jwt.verify(token, JWT_SECRET) as any

    // 从数据库验证用户是否仍然有效
    const { data: user, error } = await supabaseAdmin
      .from(TABLES.ADMIN_USERS)
      .select('id, email, name, role')
      .eq('id', decoded.userId)
      .eq('is_active', true)
      .single()

    if (error || !user) {
      return {
        success: false,
        error: 'Invalid token'
      }
    }

    return {
      success: true,
      user: {
        id: user.id,
        email: user.email,
        name: user.name,
        role: user.role
      }
    }
  } catch (error) {
    return {
      success: false,
      error: 'Invalid or expired token'
    }
  }
}

// 创建管理员用户
export async function createAdminUser(email: string, password: string, name?: string): Promise<AuthResult> {
  try {
    // 检查邮箱是否已存在
    const { data: existingUser } = await supabaseAdmin
      .from(TABLES.ADMIN_USERS)
      .select('id')
      .eq('email', email)
      .single()

    if (existingUser) {
      return {
        success: false,
        error: 'Email already exists'
      }
    }

    // 加密密码
    const passwordHash = await bcrypt.hash(password, 10)

    // 创建用户
    const { data: user, error } = await supabaseAdmin
      .from(TABLES.ADMIN_USERS)
      .insert({
        email,
        password_hash: passwordHash,
        name,
        role: 'admin'
      })
      .select('id, email, name, role')
      .single()

    if (error) {
      console.error('Create user error:', error)
      return {
        success: false,
        error: 'Failed to create user'
      }
    }

    return {
      success: true,
      user: {
        id: user.id,
        email: user.email,
        name: user.name,
        role: user.role
      }
    }
  } catch (error) {
    console.error('Create admin user error:', error)
    return {
      success: false,
      error: 'Failed to create admin user'
    }
  }
}

// 中间件：验证管理员权限
export function requireAuth() {
  return async (request: Request) => {
    const authHeader = request.headers.get('authorization')
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return {
        success: false,
        error: 'No token provided'
      }
    }

    const token = authHeader.substring(7)
    return await verifyToken(token)
  }
}
