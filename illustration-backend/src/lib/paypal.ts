// PayPal 集成辅助函数
// 这些函数将在未来集成 PayPal 支付时使用

const PAYPAL_CLIENT_ID = process.env.PAYPAL_CLIENT_ID
const PAYPAL_CLIENT_SECRET = process.env.PAYPAL_CLIENT_SECRET
const PAYPAL_BASE_URL = process.env.NODE_ENV === 'production' 
  ? 'https://api.paypal.com' 
  : 'https://api.sandbox.paypal.com'

export interface PayPalAccessToken {
  access_token: string
  token_type: string
  expires_in: number
}

export interface PayPalOrder {
  id: string
  status: string
  links: Array<{
    href: string
    rel: string
    method: string
  }>
}

export interface CreateOrderRequest {
  orderId: string
  amount: number
  currency: string
  description: string
  returnUrl: string
  cancelUrl: string
}

// 获取 PayPal 访问令牌
export async function getPayPalAccessToken(): Promise<PayPalAccessToken> {
  const auth = Buffer.from(`${PAYPAL_CLIENT_ID}:${PAYPAL_CLIENT_SECRET}`).toString('base64')
  
  const response = await fetch(`${PAYPAL_BASE_URL}/v1/oauth2/token`, {
    method: 'POST',
    headers: {
      'Authorization': `Basic ${auth}`,
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    body: 'grant_type=client_credentials'
  })

  if (!response.ok) {
    throw new Error('Failed to get PayPal access token')
  }

  return await response.json()
}

// 创建 PayPal 订单
export async function createPayPalOrder(orderData: CreateOrderRequest): Promise<PayPalOrder> {
  const accessToken = await getPayPalAccessToken()
  
  const order = {
    intent: 'CAPTURE',
    purchase_units: [{
      custom_id: orderData.orderId, // 我们的订单ID
      description: orderData.description,
      amount: {
        currency_code: orderData.currency,
        value: orderData.amount.toFixed(2)
      }
    }],
    application_context: {
      return_url: orderData.returnUrl,
      cancel_url: orderData.cancelUrl,
      shipping_preference: 'NO_SHIPPING',
      user_action: 'PAY_NOW'
    }
  }

  const response = await fetch(`${PAYPAL_BASE_URL}/v2/checkout/orders`, {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${accessToken.access_token}`,
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(order)
  })

  if (!response.ok) {
    const error = await response.text()
    throw new Error(`Failed to create PayPal order: ${error}`)
  }

  return await response.json()
}

// 捕获 PayPal 订单支付
export async function capturePayPalOrder(orderId: string): Promise<any> {
  const accessToken = await getPayPalAccessToken()
  
  const response = await fetch(`${PAYPAL_BASE_URL}/v2/checkout/orders/${orderId}/capture`, {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${accessToken.access_token}`,
      'Content-Type': 'application/json',
    }
  })

  if (!response.ok) {
    const error = await response.text()
    throw new Error(`Failed to capture PayPal order: ${error}`)
  }

  return await response.json()
}

// 获取 PayPal 订单详情
export async function getPayPalOrderDetails(orderId: string): Promise<any> {
  const accessToken = await getPayPalAccessToken()
  
  const response = await fetch(`${PAYPAL_BASE_URL}/v2/checkout/orders/${orderId}`, {
    method: 'GET',
    headers: {
      'Authorization': `Bearer ${accessToken.access_token}`,
      'Content-Type': 'application/json',
    }
  })

  if (!response.ok) {
    const error = await response.text()
    throw new Error(`Failed to get PayPal order details: ${error}`)
  }

  return await response.json()
}

// 创建退款
export async function createPayPalRefund(captureId: string, amount?: number): Promise<any> {
  const accessToken = await getPayPalAccessToken()
  
  const refundData: any = {}
  if (amount) {
    refundData.amount = {
      currency_code: 'USD',
      value: amount.toFixed(2)
    }
  }

  const response = await fetch(`${PAYPAL_BASE_URL}/v2/payments/captures/${captureId}/refund`, {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${accessToken.access_token}`,
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(refundData)
  })

  if (!response.ok) {
    const error = await response.text()
    throw new Error(`Failed to create PayPal refund: ${error}`)
  }

  return await response.json()
}
