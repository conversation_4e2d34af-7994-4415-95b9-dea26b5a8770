import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'
import jwt from 'jsonwebtoken'

// 简化的token验证函数，支持演示模式
function verifyTokenSimple(token: string): boolean {
  try {
    const JWT_SECRET = process.env.JWT_SECRET || 'demo_jwt_secret_key_for_development_only'
    const decoded = jwt.verify(token, JWT_SECRET) as any
    return true // 如果能解码就认为有效
  } catch (error) {
    return false
  }
}

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl

  // 只对 /admin 路径进行保护，但排除登录页面
  if (pathname.startsWith('/admin') && pathname !== '/admin/login') {
    const token = request.cookies.get('admin-token')?.value

    if (!token) {
      // 没有token，重定向到登录页
      return NextResponse.redirect(new URL('/admin/login', request.url))
    }

    // 使用简化的验证方法
    if (!verifyTokenSimple(token)) {
      // token无效，重定向到登录页
      const response = NextResponse.redirect(new URL('/admin/login', request.url))
      response.cookies.delete('admin-token')
      return response
    }

    // token有效，继续请求
    return NextResponse.next()
  }

  // 如果已经登录，访问登录页面时重定向到管理后台
  if (pathname === '/admin/login') {
    const token = request.cookies.get('admin-token')?.value

    if (token && verifyTokenSimple(token)) {
      return NextResponse.redirect(new URL('/admin', request.url))
    }
  }

  return NextResponse.next()
}

export const config = {
  matcher: [
    '/admin/:path*'
  ]
}
