export interface Order {
  id: string
  order_id: string // 唯一订单ID
  service_type: 'brand' | 'character' | 'scene'
  service_name: string
  price: number
  
  // 客户信息
  customer_name?: string
  customer_email?: string
  customer_contact: string
  
  // 订单内容
  requirements: string
  
  // 文件信息
  photos: string[] // 照片文件URLs
  style_images: string[] // 风格参考图URLs
  
  // 支付信息
  paypal_transaction_id?: string
  payment_status: 'pending' | 'completed' | 'failed' | 'refunded'
  
  // 订单状态
  order_status: 'pending' | 'in_progress' | 'completed' | 'cancelled'
  
  // 时间戳
  created_at: string
  updated_at: string
}

export interface CreateOrderRequest {
  service_type: string
  service_name: string
  price: number
  customer_contact: string
  requirements: string
  photos: File[]
  style_images: File[]
}

export interface OrderListResponse {
  orders: Order[]
  total: number
  page: number
  limit: number
}

export interface FileUploadResponse {
  success: boolean
  urls: string[]
  error?: string
}
