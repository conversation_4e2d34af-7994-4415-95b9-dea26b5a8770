-- 创建订单表
CREATE TABLE IF NOT EXISTS orders (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    order_id VARCHAR(50) UNIQUE NOT NULL, -- 唯一订单ID，如 ORD-1234567890-ABC123
    
    -- 服务信息
    service_type VARCHAR(20) NOT NULL CHECK (service_type IN ('brand', 'character', 'scene')),
    service_name VARCHAR(100) NOT NULL,
    price DECIMAL(10,2) NOT NULL,
    
    -- 客户信息
    customer_name VARCHAR(100),
    customer_email VARCHAR(255),
    customer_contact VARCHAR(100) NOT NULL,
    
    -- 订单内容
    requirements TEXT NOT NULL,
    
    -- 文件信息（存储文件URL数组）
    photos TEXT[] DEFAULT '{}', -- 客户照片URLs
    style_images TEXT[] DEFAULT '{}', -- 风格参考图URLs
    
    -- 支付信息
    paypal_transaction_id VARCHAR(100),
    payment_status VARCHAR(20) DEFAULT 'pending' CHECK (payment_status IN ('pending', 'completed', 'failed', 'refunded')),
    
    -- 订单状态
    order_status VARCHAR(20) DEFAULT 'pending' CHECK (order_status IN ('pending', 'in_progress', 'completed', 'cancelled')),
    
    -- 时间戳
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建管理员用户表
CREATE TABLE IF NOT EXISTS admin_users (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    name VARCHAR(100),
    role VARCHAR(20) DEFAULT 'admin' CHECK (role IN ('admin', 'super_admin')),
    is_active BOOLEAN DEFAULT true,
    last_login TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_orders_order_id ON orders(order_id);
CREATE INDEX IF NOT EXISTS idx_orders_created_at ON orders(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_orders_service_type ON orders(service_type);
CREATE INDEX IF NOT EXISTS idx_orders_order_status ON orders(order_status);
CREATE INDEX IF NOT EXISTS idx_orders_payment_status ON orders(payment_status);

-- 创建更新时间触发器
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_orders_updated_at BEFORE UPDATE ON orders
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_admin_users_updated_at BEFORE UPDATE ON admin_users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 创建存储桶（需要在 Supabase 控制台中手动创建，或通过 API）
-- customer-photos: 存储客户上传的照片
-- style-references: 存储风格参考图

-- 插入默认管理员用户（密码需要用 bcrypt 加密）
-- 默认密码: admin123 (请在生产环境中更改)
INSERT INTO admin_users (email, password_hash, name, role) 
VALUES (
    '<EMAIL>',
    '$2b$10$rQZ8kHWf5r.Qx8ZxGxGxGOyKQqQqQqQqQqQqQqQqQqQqQqQqQqQqQ', -- 这是 'admin123' 的 bcrypt hash
    'System Administrator',
    'super_admin'
) ON CONFLICT (email) DO NOTHING;
