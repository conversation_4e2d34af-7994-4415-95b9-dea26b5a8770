# 数据库设置说明

## 1. Supabase 项目设置

1. 访问 [Supabase](https://supabase.com) 并创建新项目
2. 记录以下信息：
   - Project URL
   - Anon Key
   - Service Role Key

## 2. 执行数据库脚本

在 Supabase 控制台的 SQL Editor 中执行 `schema.sql` 文件中的所有 SQL 语句。

## 3. 创建存储桶

在 Supabase 控制台的 Storage 部分创建以下存储桶：

### customer-photos
- 名称: `customer-photos`
- 公开访问: 是
- 文件大小限制: 5MB
- 允许的文件类型: image/jpeg, image/png, image/webp

### style-references  
- 名称: `style-references`
- 公开访问: 是
- 文件大小限制: 5MB
- 允许的文件类型: image/jpeg, image/png, image/webp

## 4. 配置存储桶策略

为每个存储桶添加以下 RLS 策略：

```sql
-- 允许所有人上传文件
CREATE POLICY "Allow public uploads" ON storage.objects
FOR INSERT WITH CHECK (bucket_id = 'customer-photos');

CREATE POLICY "Allow public uploads" ON storage.objects
FOR INSERT WITH CHECK (bucket_id = 'style-references');

-- 允许所有人查看文件
CREATE POLICY "Allow public access" ON storage.objects
FOR SELECT USING (bucket_id = 'customer-photos');

CREATE POLICY "Allow public access" ON storage.objects
FOR SELECT USING (bucket_id = 'style-references');
```

## 5. 环境变量配置

复制 `.env.example` 为 `.env.local` 并填入相应的值：

```bash
cp .env.example .env.local
```

## 6. 管理员账户

默认管理员账户：
- 邮箱: <EMAIL>
- 密码: admin123

**重要**: 请在生产环境中立即更改默认密码！

## 7. 测试连接

运行以下命令测试数据库连接：

```bash
npm run dev
```

访问 `http://localhost:3000/api/test` 检查 API 是否正常工作。
