{"name": "illustration-backend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@radix-ui/react-slot": "^1.0.2", "@supabase/storage-js": "^2.5.5", "@supabase/supabase-js": "^2.38.0", "bcryptjs": "^2.4.3", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "formidable": "^3.5.1", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.294.0", "multer": "^2.0.2", "next": "^14.2.30", "nodemailer": "^6.9.7", "react": "^18", "react-dom": "^18", "tailwind-merge": "^2.0.0", "tailwindcss-animate": "^1.0.7", "uuid": "^9.0.1"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/formidable": "^3.4.5", "@types/jsonwebtoken": "^9.0.5", "@types/multer": "^2.0.0", "@types/node": "^20", "@types/nodemailer": "^6.4.14", "@types/react": "^18", "@types/react-dom": "^18", "@types/uuid": "^9.0.7", "autoprefixer": "^10.0.1", "eslint": "^8", "eslint-config-next": "14.0.0", "postcss": "^8", "tailwindcss": "^3.3.0", "typescript": "^5"}}