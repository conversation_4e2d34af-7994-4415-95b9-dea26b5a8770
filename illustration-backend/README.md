# 插画工作室订单管理系统

基于 Next.js App Router + Supabase 构建的现代化订单管理后端系统。

## 🚀 功能特性

### 核心功能
- ✅ **订单管理** - 完整的订单 CRUD 操作
- ✅ **文件上传** - 支持客户照片和风格参考图上传
- ✅ **管理后台** - 基于 shadcn/ui 的现代化管理界面
- ✅ **身份验证** - JWT + Cookie 的安全认证系统
- ✅ **PayPal 集成** - 预留支付接口和 Webhook 处理

### 技术栈
- **前端**: Next.js 14 (App Router) + React 18 + TypeScript
- **UI 组件**: shadcn/ui + Tailwind CSS
- **数据库**: Supabase (PostgreSQL)
- **文件存储**: Supabase Storage
- **身份验证**: JWT + bcryptjs
- **支付**: PayPal (预留接口)
- **部署**: Vercel

## 📦 项目结构

```
illustration-backend/
├── src/
│   ├── app/
│   │   ├── admin/                 # 管理后台页面
│   │   │   ├── login/            # 登录页面
│   │   │   ├── orders/[id]/      # 订单详情页面
│   │   │   └── page.tsx          # 管理后台首页
│   │   ├── api/                  # API 路由
│   │   │   ├── auth/             # 身份验证接口
│   │   │   ├── orders/           # 订单管理接口
│   │   │   ├── paypal/           # PayPal 集成接口
│   │   │   └── upload/           # 文件上传接口
│   │   ├── globals.css           # 全局样式
│   │   ├── layout.tsx            # 根布局
│   │   └── page.tsx              # 首页
│   ├── components/ui/            # UI 组件库
│   ├── lib/                      # 工具库
│   │   ├── auth.ts              # 身份验证逻辑
│   │   ├── file-upload.ts       # 文件上传逻辑
│   │   ├── paypal.ts            # PayPal 集成
│   │   ├── supabase.ts          # 数据库连接
│   │   └── utils.ts             # 通用工具函数
│   └── types/                    # TypeScript 类型定义
├── database/                     # 数据库相关
│   ├── schema.sql               # 数据库表结构
│   └── setup-instructions.md   # 数据库设置说明
├── .env.example                 # 环境变量示例
├── next.config.js              # Next.js 配置
├── tailwind.config.js          # Tailwind CSS 配置
└── vercel.json                 # Vercel 部署配置
```

## 🛠️ 快速开始

### 1. 环境准备

```bash
# 克隆项目
git clone <repository-url>
cd illustration-backend

# 安装依赖
npm install

# 复制环境变量文件
cp .env.example .env.local
```

### 2. 配置 Supabase

1. 访问 [Supabase](https://supabase.com) 创建新项目
2. 在 SQL Editor 中执行 `database/schema.sql`
3. 在 Storage 中创建存储桶：
   - `customer-photos`
   - `style-references`
4. 配置存储桶的 RLS 策略（参考 `database/setup-instructions.md`）

### 3. 配置环境变量

编辑 `.env.local` 文件：

```env
# Supabase 配置
SUPABASE_URL=your_supabase_project_url
SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key

# JWT 密钥
JWT_SECRET=your_jwt_secret_key

# PayPal 配置（可选）
PAYPAL_CLIENT_ID=your_paypal_client_id
PAYPAL_CLIENT_SECRET=your_paypal_client_secret
```

### 4. 启动开发服务器

```bash
npm run dev
```

访问 `http://localhost:3000/admin` 进入管理后台。

默认管理员账户：
- 邮箱: `<EMAIL>`
- 密码: `admin123`

## 📡 API 接口

### 订单管理
- `GET /api/orders` - 获取订单列表
- `POST /api/orders` - 创建新订单
- `GET /api/orders/[id]` - 获取订单详情
- `PATCH /api/orders/[id]` - 更新订单状态
- `DELETE /api/orders/[id]` - 删除订单

### 身份验证
- `POST /api/auth/login` - 管理员登录
- `POST /api/auth/logout` - 登出
- `GET /api/auth/verify` - 验证登录状态

### 文件上传
- `POST /api/upload` - 通用文件上传接口

### PayPal 集成（预留）
- `POST /api/paypal/create-order` - 创建支付订单
- `POST /api/paypal/webhook` - 接收支付状态更新

## 🚀 部署到 Vercel

### 1. 连接 GitHub

1. 将代码推送到 GitHub 仓库
2. 在 Vercel 中导入项目

### 2. 配置环境变量

在 Vercel 项目设置中添加环境变量：

```
SUPABASE_URL
SUPABASE_ANON_KEY
SUPABASE_SERVICE_ROLE_KEY
JWT_SECRET
PAYPAL_CLIENT_ID (可选)
PAYPAL_CLIENT_SECRET (可选)
PAYPAL_WEBHOOK_ID (可选)
```

### 3. 部署

Vercel 会自动构建和部署项目。

## 🔒 安全考虑

1. **环境变量**: 敏感信息存储在环境变量中
2. **JWT 认证**: 使用 HTTP-only Cookie 存储 JWT
3. **文件上传**: 限制文件类型和大小
4. **数据库**: 使用 Supabase RLS 策略保护数据
5. **PayPal Webhook**: 需要验证签名（生产环境）

## 🔄 未来扩展

### 即将实现的功能
- [ ] 邮件通知系统
- [ ] 订单状态自动化工作流
- [ ] 批量操作功能
- [ ] 数据导出功能
- [ ] 客户端订单查询页面

### PayPal 集成步骤
1. 配置 PayPal 开发者账户
2. 获取 Client ID 和 Secret
3. 配置 Webhook URL
4. 实现前端支付流程
5. 测试支付和退款功能

## 📞 技术支持

如有问题，请查看：
1. `database/setup-instructions.md` - 数据库设置说明
2. Supabase 官方文档
3. Next.js 官方文档
4. PayPal 开发者文档

## 📄 许可证

MIT License
