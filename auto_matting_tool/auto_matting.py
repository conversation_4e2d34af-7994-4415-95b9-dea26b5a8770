#!/usr/bin/env python3
"""
高质量自动抠图工具 - 支持边缘预留
使用 rembg (U²-Net) 模型进行抠图，并支持自定义边缘预留像素
"""

import os
import cv2
import numpy as np
from PIL import Image
import argparse
from pathlib import Path
import time
import io

def install_dependencies():
    """安装必要的依赖"""
    import subprocess
    import sys
    
    packages = [
        'rembg[new]',
        'opencv-python',
        'pillow',
        'numpy'
    ]
    
    for package in packages:
        try:
            subprocess.check_call([sys.executable, '-m', 'pip', 'install', package])
            print(f"✓ 已安装: {package}")
        except subprocess.CalledProcessError:
            print(f"✗ 安装失败: {package}")

def get_model_info(model_name):
    """获取模型信息"""
    model_info = {
        'u2net': {'name': 'U²-Net', 'size': '176MB', 'scene': '通用场景'},
        'u2netp': {'name': 'U²-Net-P', 'size': '4.7MB', 'scene': '轻量快速'},
        'silueta': {'name': 'Silueta', 'size': '176MB', 'scene': '人像专用'},
        'isnet-general-use': {'name': 'ISNet', 'size': '173MB', 'scene': '通用增强'},
        'rmbg-1.4': {'name': 'RMBG-1.4', 'size': '176MB', 'scene': '复杂场景人像'}
    }
    return model_info.get(model_name, {'name': model_name, 'size': '未知', 'scene': '未知'})

def process_image_with_edge_padding(input_path, output_path, edge_padding=10, model_name='u2net'):
    """
    处理单张图片，进行抠图并添加边缘预留
    
    Args:
        input_path: 输入图片路径
        output_path: 输出图片路径
        edge_padding: 边缘预留像素数
        model_name: 使用的模型名称
    """
    try:
        from rembg import remove, new_session
        
        # 创建会话
        session = new_session(model_name)
        
        # 读取原图
        with open(input_path, 'rb') as f:
            input_data = f.read()
        
        # 使用 rembg 进行抠图
        output_data = remove(input_data, session=session)
        
        # 转换为 PIL 图像
        img_with_alpha = Image.open(io.BytesIO(output_data)).convert('RGBA')
        original_img = Image.open(input_path).convert('RGB')
        
        # 转换为 numpy 数组进行处理
        alpha_array = np.array(img_with_alpha)[:, :, 3]  # 获取 alpha 通道
        original_array = np.array(original_img)
        
        # 如果需要边缘预留，对 mask 进行膨胀操作
        if edge_padding > 0:
            # 创建膨胀核
            kernel = np.ones((edge_padding * 2 + 1, edge_padding * 2 + 1), np.uint8)
            # 对 alpha 通道进行膨胀操作
            dilated_mask = cv2.dilate(alpha_array, kernel, iterations=1)
            alpha_array = dilated_mask
        
        # 创建最终的 RGBA 图像
        result = np.zeros((original_array.shape[0], original_array.shape[1], 4), dtype=np.uint8)
        result[:, :, :3] = original_array  # RGB 通道
        result[:, :, 3] = alpha_array      # Alpha 通道
        
        # 保存结果
        result_img = Image.fromarray(result, 'RGBA')
        result_img.save(output_path, 'PNG')
        
        return True
        
    except Exception as e:
        print(f"处理图片失败 {input_path}: {str(e)}")
        return False

def batch_process(input_dir, output_dir, edge_padding=10, model_name='u2net'):
    """
    批量处理图片
    
    Args:
        input_dir: 输入目录
        output_dir: 输出目录
        edge_padding: 边缘预留像素数
        model_name: 使用的模型名称
    """
    # 支持的图片格式
    supported_formats = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.webp'}
    
    input_path = Path(input_dir)
    output_path = Path(output_dir)
    
    # 创建输出目录
    output_path.mkdir(parents=True, exist_ok=True)
    
    # 获取所有图片文件
    image_files = []
    for ext in supported_formats:
        image_files.extend(input_path.glob(f'*{ext}'))
        image_files.extend(input_path.glob(f'*{ext.upper()}'))
    
    if not image_files:
        print(f"在 {input_dir} 中没有找到支持的图片文件")
        return
    
    # 获取模型信息
    model_info = get_model_info(model_name)

    print(f"找到 {len(image_files)} 张图片，开始处理...")
    print(f"边缘预留: {edge_padding} 像素")
    print(f"使用模型: {model_info['name']} ({model_name})")
    print(f"模型大小: {model_info['size']}")
    print(f"适用场景: {model_info['scene']}")
    print("-" * 50)
    
    success_count = 0
    total_time = 0
    
    for i, img_file in enumerate(image_files, 1):
        start_time = time.time()
        
        # 生成输出文件名
        output_file = output_path / f"{img_file.stem}_matted.png"
        
        print(f"[{i}/{len(image_files)}] 处理: {img_file.name}")
        
        # 处理图片
        if process_image_with_edge_padding(str(img_file), str(output_file), edge_padding, model_name):
            process_time = time.time() - start_time
            total_time += process_time
            success_count += 1
            print(f"  ✓ 完成 ({process_time:.2f}s) -> {output_file.name}")
        else:
            print(f"  ✗ 失败")
        
        print()
    
    print("-" * 50)
    print(f"处理完成!")
    print(f"成功: {success_count}/{len(image_files)}")
    print(f"总耗时: {total_time:.2f}s")
    print(f"平均耗时: {total_time/len(image_files):.2f}s/张")
    print(f"输出目录: {output_dir}")

def main():
    parser = argparse.ArgumentParser(description='高质量自动抠图工具')
    parser.add_argument('--input', '-i', required=True, help='输入图片文件或目录')
    parser.add_argument('--output', '-o', required=True, help='输出文件或目录')
    parser.add_argument('--padding', '-p', type=int, default=10, help='边缘预留像素数 (默认: 10)')
    parser.add_argument('--model', '-m', default='u2net',
                       choices=['u2net', 'u2netp', 'silueta', 'isnet-general-use', 'rmbg-1.4', 'sam'],
                       help='使用的模型 (默认: u2net, 推荐复杂场景: rmbg-1.4, 最强技术: sam)')
    parser.add_argument('--install', action='store_true', help='安装依赖包')
    
    args = parser.parse_args()
    
    if args.install:
        print("正在安装依赖包...")
        install_dependencies()
        return
    
    # 检查输入路径
    if not os.path.exists(args.input):
        print(f"错误: 输入路径不存在: {args.input}")
        return
    
    # 判断是单文件还是目录
    if os.path.isfile(args.input):
        # 单文件处理
        print(f"处理单个文件: {args.input}")
        if process_image_with_edge_padding(args.input, args.output, args.padding, args.model):
            print(f"✓ 处理完成: {args.output}")
        else:
            print("✗ 处理失败")
    else:
        # 批量处理
        batch_process(args.input, args.output, args.padding, args.model)

if __name__ == '__main__':
    main()
