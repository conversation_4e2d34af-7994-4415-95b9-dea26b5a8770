#!/usr/bin/env python3
"""
无限队列抠图工具 - 真正的无限连续处理
可以无限制添加文件到队列，静默处理
"""

import os
import sys
import glob
import threading
import queue
import time
from config import load_config, get_output_path, show_current_config, modify_config, show_presets, apply_preset

def clean_path(path):
    """清理路径中的特殊字符"""
    path = path.strip()
    if path.startswith('"') and path.endswith('"'):
        path = path[1:-1]  # 去掉首尾引号
    
    # 处理转义字符
    path = path.replace('\\ ', ' ')  # 处理转义的空格
    path = path.replace('\\(', '(')  # 处理转义的左括号
    path = path.replace('\\)', ')')  # 处理转义的右括号
    return path

class InfiniteProcessor:
    def __init__(self):
        self.task_queue = queue.Queue()
        self.processing = False
        self.current_task = None
        self.total_processed = 0
        self.total_failed = 0
        self.worker_thread = None
        self.running = True
        self.paused = False
        
    def start_worker(self):
        """启动工作线程"""
        self.worker_thread = threading.Thread(target=self.worker)
        self.worker_thread.daemon = True
        self.worker_thread.start()
    
    def worker(self):
        """工作线程 - 持续处理队列中的任务"""
        while self.running:
            try:
                # 检查是否暂停
                if self.paused:
                    time.sleep(1)
                    continue

                # 从队列获取任务，超时1秒
                task = self.task_queue.get(timeout=1)
                self.current_task = task
                self.processing = True

                # 处理任务
                if task['type'] == 'single':
                    success = self.process_single_image_silent(task['path'])
                elif task['type'] == 'batch':
                    success = self.process_batch_silent(task['path'])
                else:
                    success = False

                # 更新统计
                if success:
                    self.total_processed += 1
                else:
                    self.total_failed += 1
                    print(f"❌ 处理失败: {os.path.basename(task['path'])}")

                self.current_task = None
                self.processing = False
                self.task_queue.task_done()

            except queue.Empty:
                continue
            except Exception as e:
                print(f"❌ 处理异常: {e}")
                self.processing = False
                self.current_task = None
    
    def add_task(self, path, task_type):
        """添加任务到队列"""
        task = {'path': path, 'type': task_type}
        self.task_queue.put(task)
        self.show_queue_status()
    
    def show_queue_status(self):
        """显示队列状态"""
        queue_size = self.task_queue.qsize()
        status_prefix = "⏸️ 暂停" if self.paused else ""

        if self.processing and not self.paused:
            current_name = os.path.basename(self.current_task['path']) if self.current_task else "未知"
            print(f"🔄 正在处理: {current_name} | 队列: {queue_size} | 已完成: {self.total_processed} | 失败: {self.total_failed}")
        elif self.paused:
            print(f"⏸️ 队列已暂停 | 队列: {queue_size} | 已完成: {self.total_processed} | 失败: {self.total_failed}")
        else:
            if queue_size > 0:
                print(f"⏳ 队列: {queue_size} | 已完成: {self.total_processed} | 失败: {self.total_failed}")
            else:
                print(f"✅ 队列空闲 | 已完成: {self.total_processed} | 失败: {self.total_failed}")

    def clear_queue(self):
        """清空队列"""
        while not self.task_queue.empty():
            try:
                self.task_queue.get_nowait()
                self.task_queue.task_done()
            except queue.Empty:
                break
        return True

    def pause(self):
        """暂停处理"""
        self.paused = True
        return True

    def resume(self):
        """恢复处理"""
        self.paused = False
        return True
    
    def process_single_image_silent(self, input_path):
        """静默处理单张图片"""
        config = load_config()
        
        # 检查文件是否存在
        if not os.path.exists(input_path):
            return False
        
        # 生成输出路径
        output_path = get_output_path(input_path, config)
        
        # 获取脚本目录
        script_dir = os.path.dirname(os.path.abspath(__file__))
        
        cmd = f"cd '{script_dir}' && python3 auto_matting.py -i '{input_path}' -o '{output_path}' -p {config['edge_padding']} -m {config['model']} >/dev/null 2>&1"
        
        result = os.system(cmd)
        
        if result == 0:
            if config['auto_open_result']:
                import subprocess
                subprocess.run(['open', os.path.dirname(output_path)])
            return True
        else:
            return False
    
    def process_batch_silent(self, input_path):
        """静默批量处理"""
        config = load_config()
        
        # 检查目录是否存在
        if not os.path.exists(input_path):
            return False
        
        # 检查是否有图片文件
        supported_formats = ['*.jpg', '*.jpeg', '*.png', '*.bmp', '*.tiff', '*.webp', 
                            '*.JPG', '*.JPEG', '*.PNG', '*.BMP', '*.TIFF', '*.WEBP']
        image_files = []
        for pattern in supported_formats:
            image_files.extend(glob.glob(os.path.join(input_path, pattern)))
        
        if not image_files:
            return False
        
        # 生成输出路径
        if config['save_location'] == 'same_folder':
            output_path = f"{input_path}_抠图结果"
        else:
            custom_path = config['custom_save_path']
            if custom_path and os.path.exists(custom_path):
                folder_name = os.path.basename(input_path)
                output_path = os.path.join(custom_path, f"{folder_name}_抠图结果")
            else:
                output_path = f"{input_path}_抠图结果"
        
        # 获取脚本目录
        script_dir = os.path.dirname(os.path.abspath(__file__))
        
        cmd = f"cd '{script_dir}' && python3 auto_matting.py -i '{input_path}' -o '{output_path}' -p {config['edge_padding']} -m {config['model']} >/dev/null 2>&1"
        
        result = os.system(cmd)
        
        if result == 0:
            if config['auto_open_result']:
                import subprocess
                subprocess.run(['open', output_path])
            return True
        else:
            return False

def handle_command(command, processor):
    """处理快捷命令"""
    parts = command.lower().strip().split()
    cmd = parts[0] if parts else ""
    
    # 基础命令
    if cmd in ['settings', 'set', 's']:
        print("\n🔧 打开设置...")
        modify_config()
        return True
    elif cmd in ['config', 'cfg', 'c']:
        show_current_config()
        return True
    elif cmd in ['status', 'st']:
        processor.show_queue_status()
        return True
    elif cmd in ['help', 'h', '?']:
        show_help()
        return True
    elif cmd in ['quit', 'exit', 'q']:
        processor.running = False
        print("👋 再见!")
        sys.exit(0)
    
    # 快捷设置命令
    elif cmd == 'model' and len(parts) == 2:
        return quick_set_model(parts[1])
    elif cmd == 'padding' and len(parts) == 2:
        return quick_set_padding(parts[1])
    elif cmd == 'format' and len(parts) == 2:
        return quick_set_format(parts[1])
    elif cmd == 'location' and len(parts) == 2:
        return quick_set_location(parts[1])
    elif cmd == 'preset' and len(parts) == 2:
        return quick_apply_preset(parts[1])
    elif cmd == 'presets':
        show_presets()
        return True
    elif cmd == 'clear' and len(parts) == 1:
        return clear_queue(processor)
    elif cmd == 'pause':
        return pause_queue(processor)
    elif cmd == 'resume':
        return resume_queue(processor)
    else:
        return False

def show_help():
    """显示帮助信息"""
    print("\n📖 快捷命令:")
    print("=" * 60)
    print("🔧 settings/set/s    - 打开完整设置菜单")
    print("📊 config/cfg/c      - 查看当前设置")
    print("📈 status/st         - 显示队列状态")
    print("❓ help/h/?          - 显示此帮助")
    print("🚪 quit/exit/q       - 退出程序")
    print()
    print("🎮 队列控制:")
    print("⏸️ pause             - 暂停队列处理")
    print("▶️ resume            - 恢复队列处理")
    print("🗑️ clear             - 清空队列")
    print()
    print("⚡ 快捷设置:")
    print("🎯 model <名称>      - 快速切换模型")
    print("📐 padding <数字>    - 快速设置边缘像素")
    print("💾 format <格式>     - 快速设置输出格式")
    print("📁 location <位置>   - 快速设置保存位置")
    print("🎨 preset <名称>     - 应用预设配置")
    print("📋 presets          - 查看所有预设")
    print()
    print("💡 示例:")
    print("  model rmbg-1.4     - 切换到RMBG-1.4模型")
    print("  padding 20         - 设置边缘为20像素")
    print("  format png         - 设置输出为PNG格式")
    print("  location same      - 保存到同目录")
    print("  preset 高质量      - 应用高质量预设")
    print("  presets            - 查看所有预设")
    print("  pause              - 暂停处理")
    print("  clear              - 清空队列")
    print("=" * 60)
    print("💡 直接拖拽图片/文件夹进行处理，可无限添加到队列")

def quick_set_model(model_name):
    """快速设置模型"""
    from config import save_config
    
    models = {
        'u2net': 'u2net',
        'rmbg': 'rmbg-1.4',
        'rmbg-1.4': 'rmbg-1.4',
        'isnet': 'isnet-general-use',
        'sam': 'sam'
    }
    
    if model_name in models:
        config = load_config()
        config['model'] = models[model_name]
        if save_config(config):
            print(f"✅ 模型已切换为: {models[model_name]}")
        else:
            print("❌ 设置保存失败")
        return True
    else:
        print(f"❌ 不支持的模型: {model_name}")
        print("💡 支持的模型: u2net, rmbg-1.4, isnet, sam")
        return True

def quick_set_padding(padding_str):
    """快速设置边缘像素"""
    from config import save_config
    
    try:
        padding = int(padding_str)
        if 0 <= padding <= 100:
            config = load_config()
            config['edge_padding'] = padding
            if save_config(config):
                print(f"✅ 边缘像素已设置为: {padding}")
            else:
                print("❌ 设置保存失败")
        else:
            print("❌ 边缘像素必须在0-100之间")
    except ValueError:
        print("❌ 请输入有效的数字")
    return True

def quick_set_format(format_name):
    """快速设置输出格式"""
    from config import save_config
    
    formats = {'png': 'png', 'jpg': 'jpg', 'jpeg': 'jpg'}
    
    if format_name in formats:
        config = load_config()
        config['output_format'] = formats[format_name]
        if save_config(config):
            print(f"✅ 输出格式已设置为: {formats[format_name].upper()}")
        else:
            print("❌ 设置保存失败")
    else:
        print(f"❌ 不支持的格式: {format_name}")
        print("💡 支持的格式: png, jpg")
    return True

def quick_set_location(location):
    """快速设置保存位置"""
    from config import save_config
    
    locations = {'same': 'same_folder', 'custom': 'custom'}
    
    if location in locations:
        config = load_config()
        config['save_location'] = locations[location]
        if save_config(config):
            if location == 'same':
                print("✅ 保存位置已设置为: 原图片同目录")
            else:
                print("✅ 保存位置已设置为: 自定义目录")
                print("💡 请使用 'settings' 命令设置具体的自定义目录")
        else:
            print("❌ 设置保存失败")
    else:
        print(f"❌ 不支持的位置: {location}")
        print("💡 支持的位置: same (同目录), custom (自定义)")
    return True

def quick_apply_preset(preset_name):
    """快速应用预设"""
    presets = {"快速": "快速", "高质量": "高质量", "最强": "最强", "批量": "批量",
               "fast": "快速", "quality": "高质量", "best": "最强", "batch": "批量"}

    preset_key = presets.get(preset_name, preset_name)

    if apply_preset(preset_key):
        print(f"✅ 已应用预设: {preset_key}")
        print("💡 输入 'config' 查看当前设置")
    else:
        print(f"❌ 预设不存在: {preset_name}")
        print("💡 输入 'presets' 查看所有可用预设")
    return True

def clear_queue(processor):
    """清空队列"""
    if processor.clear_queue():
        print("✅ 队列已清空")
        processor.show_queue_status()
    else:
        print("❌ 清空队列失败")
    return True

def pause_queue(processor):
    """暂停队列"""
    if processor.pause():
        print("⏸️ 队列已暂停")
        processor.show_queue_status()
    else:
        print("❌ 暂停失败")
    return True

def resume_queue(processor):
    """恢复队列"""
    if processor.resume():
        print("▶️ 队列已恢复")
        processor.show_queue_status()
    else:
        print("❌ 恢复失败")
    return True

def main():
    print("🚀 无限队列抠图工具")
    print("=" * 50)
    print("💡 使用说明:")
    print("  • 可以无限制拖拽图片/文件夹到队列")
    print("  • 只有失败时才会通知，成功时静默处理")
    print("  • 输入 'status' 查看队列状态")
    print("  • 输入 'help' 查看更多命令")
    print("  • 按 Ctrl+C 或输入 'quit' 退出")
    print("=" * 50)
    
    # 显示当前配置
    config = load_config()
    print(f"⚙️ 当前设置: {config['edge_padding']}像素边缘 | {config['model'].upper()}模型 | {config['output_format'].upper()}格式")
    print()
    
    # 创建处理器并启动工作线程
    processor = InfiniteProcessor()
    processor.start_worker()
    
    try:
        while True:
            print("📎 拖拽文件/文件夹，或输入命令:")
            user_input = input(">>> ").strip()
            
            if not user_input:
                processor.show_queue_status()
                continue
            
            # 检查是否是快捷命令
            if handle_command(user_input, processor):
                continue
            
            # 处理为文件路径
            input_path = clean_path(user_input)
            
            # 判断是文件还是目录
            if os.path.isfile(input_path):
                processor.add_task(input_path, 'single')
            elif os.path.isdir(input_path):
                processor.add_task(input_path, 'batch')
            else:
                print("❌ 路径不存在或不是有效的文件/文件夹")
                continue
            
    except KeyboardInterrupt:
        processor.running = False
        print("\n👋 再见!")
        sys.exit(0)
    except Exception as e:
        print(f"❌ 发生错误: {e}")
        processor.running = False

if __name__ == "__main__":
    main()
