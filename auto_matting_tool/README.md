# 🎯 高质量自动抠图工具

一个基于 AI 的自动抠图工具，支持**边缘预留功能**，让你可以控制抠图时保留多少像素的背景边缘。

## ✨ 特色功能

- 🤖 **全自动抠图**：基于 U²-Net 深度学习模型，无需手动操作
- 🎛️ **边缘预留控制**：可以设置保留 5-50 像素的背景边缘
- 📁 **批量处理**：支持整个文件夹的图片批量处理
- 🚀 **高质量模型**：使用成熟可靠的 rembg 库，效果优秀
- 💯 **完全免费**：基于开源技术，无需付费

## 🛠️ 安装依赖

首次使用需要安装依赖包：

```bash
# 方法1：自动安装
python3 run_matting.py

# 方法2：手动安装
pip install rembg[new] opencv-python pillow numpy onnxruntime
```

## 🚀 快速使用

### 1. 批量处理（推荐）

```bash
# 处理当前目录下的所有图片，边缘预留15像素
python3 run_matting.py
```

### 2. 处理单个文件

```bash
# 处理单张图片
python3 auto_matting.py -i input.jpg -o output.png -p 10
```

### 3. 处理指定文件夹

```bash
# 处理指定文件夹，边缘预留20像素
python3 auto_matting.py -i /path/to/images -o /path/to/output -p 20
```

## ⚙️ 参数说明

| 参数 | 说明 | 默认值 | 示例 |
|------|------|--------|------|
| `-i, --input` | 输入文件或文件夹 | 必填 | `-i images/` |
| `-o, --output` | 输出文件或文件夹 | 必填 | `-o results/` |
| `-p, --padding` | 边缘预留像素数 | 10 | `-p 15` |
| `-m, --model` | 使用的AI模型 | u2net | `-m u2netp` |

## 🎨 可用模型

- **u2net**：通用模型，适合大部分场景（推荐）
- **u2netp**：轻量版本，速度更快
- **silueta**：专门用于人像抠图
- **isnet-general-use**：新一代通用模型

## 📊 边缘预留效果

- **0像素**：传统抠图，边缘干净利落
- **5-10像素**：轻微保留，适合精细场景
- **10-20像素**：标准预留，适合大部分需求
- **20-50像素**：大幅预留，适合特殊效果

## 📁 文件结构

```
auto_matting_tool/
├── auto_matting.py     # 核心抠图脚本
├── run_matting.py      # 快速运行脚本
└── README.md           # 使用说明
```

## 🔧 高级用法

### 自定义批量处理

```python
from auto_matting import batch_process

# 自定义参数批量处理
batch_process(
    input_dir="my_images",
    output_dir="my_results", 
    edge_padding=25,
    model_name="silueta"
)
```

### 单图片处理

```python
from auto_matting import process_image_with_edge_padding

# 处理单张图片
success = process_image_with_edge_padding(
    input_path="photo.jpg",
    output_path="result.png",
    edge_padding=15
)
```

## 🎯 使用场景

- **电商产品图**：保留一点背景，显得更自然
- **人像处理**：避免过度抠图造成的不自然感
- **设计素材**：为后期合成预留调整空间
- **批量处理**：大量图片的自动化处理

## 📝 注意事项

1. 首次运行会下载AI模型（约50MB），需要网络连接
2. 处理大图片时可能需要较多内存
3. 输出格式固定为PNG（支持透明背景）
4. 支持输入格式：JPG, PNG, WEBP, BMP, TIFF

## 🆘 常见问题

**Q: 提示缺少依赖包？**
A: 运行 `python3 run_matting.py` 会自动安装

**Q: 处理速度慢？**
A: 可以尝试使用 `u2netp` 模型，速度更快

**Q: 抠图效果不理想？**
A: 可以尝试不同的模型，或调整边缘预留参数

**Q: 内存不足？**
A: 可以分批处理，或者压缩图片尺寸后再处理

---

🎉 **享受高质量的自动抠图体验！**
