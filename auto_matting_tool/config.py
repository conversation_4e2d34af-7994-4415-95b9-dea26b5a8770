#!/usr/bin/env python3
"""
抠图工具配置管理
"""

import json
import os

# 默认配置
DEFAULT_CONFIG = {
    "edge_padding": 15,                    # 边缘预留像素
    "model": "rmbg-1.4",                  # 使用的模型
    "save_location": "same_folder",        # 保存位置: same_folder(同目录) / custom(自定义)
    "custom_save_path": "",               # 自定义保存路径
    "filename_format": "{name}_抠图",      # 文件名格式
    "output_format": "png",               # 输出格式
    "auto_open_result": False,            # 处理完是否自动打开结果文件夹
    "batch_subfolder": True,              # 批量处理时是否创建子文件夹
    "overwrite_existing": False,          # 是否覆盖已存在的文件
    "show_progress": True,                # 是否显示处理进度
    "quality": 95,                        # JPG输出质量 (1-100)
    "resize_output": False,               # 是否调整输出尺寸
    "max_width": 1920,                    # 最大宽度 (resize_output=True时生效)
    "max_height": 1080                    # 最大高度 (resize_output=True时生效)
}

CONFIG_FILE = "matting_config.json"

def load_config():
    """加载配置"""
    config_path = os.path.join(os.path.dirname(__file__), CONFIG_FILE)
    
    if os.path.exists(config_path):
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
                # 合并默认配置，确保所有键都存在
                for key, value in DEFAULT_CONFIG.items():
                    if key not in config:
                        config[key] = value
                return config
        except:
            pass
    
    return DEFAULT_CONFIG.copy()

def save_config(config):
    """保存配置"""
    config_path = os.path.join(os.path.dirname(__file__), CONFIG_FILE)
    
    try:
        with open(config_path, 'w', encoding='utf-8') as f:
            json.dump(config, f, ensure_ascii=False, indent=2)
        return True
    except:
        return False

def get_output_path(input_path, config):
    """根据配置生成输出路径"""
    from pathlib import Path
    
    input_file = Path(input_path)
    
    # 生成文件名
    filename = config['filename_format'].format(name=input_file.stem)
    output_filename = f"{filename}.{config['output_format']}"
    
    # 确定保存目录
    if config['save_location'] == 'same_folder':
        output_path = input_file.parent / output_filename
    else:
        custom_path = config['custom_save_path']
        if custom_path and os.path.exists(custom_path):
            output_path = Path(custom_path) / output_filename
        else:
            # 如果自定义路径不存在，回退到同目录
            output_path = input_file.parent / output_filename
    
    return str(output_path)

def show_current_config():
    """显示当前配置"""
    config = load_config()

    print("\n⚙️ 当前设置:")
    print("=" * 50)

    # 基础设置
    print("📐 基础设置:")
    print(f"  边缘预留像素: {config['edge_padding']}")
    print(f"  使用模型: {config['model'].upper()}")

    # 保存设置
    print("\n💾 保存设置:")
    if config['save_location'] == 'same_folder':
        print(f"  保存位置: 原图片同目录")
    else:
        print(f"  保存位置: {config['custom_save_path']}")

    print(f"  文件名格式: {config['filename_format']}.{config['output_format']}")
    print(f"  输出格式: {config['output_format'].upper()}")

    if config['output_format'] == 'jpg':
        print(f"  JPG质量: {config['quality']}%")

    # 批量处理设置
    print("\n📁 批量处理:")
    print(f"  创建子文件夹: {'是' if config['batch_subfolder'] else '否'}")
    print(f"  覆盖已存在文件: {'是' if config['overwrite_existing'] else '否'}")

    # 显示设置
    print("\n🖥️ 显示设置:")
    print(f"  显示处理进度: {'是' if config['show_progress'] else '否'}")
    print(f"  自动打开结果: {'是' if config['auto_open_result'] else '否'}")

    # 输出尺寸设置
    print("\n📏 输出尺寸:")
    if config['resize_output']:
        print(f"  调整输出尺寸: 是 (最大 {config['max_width']}x{config['max_height']})")
    else:
        print(f"  调整输出尺寸: 否 (保持原尺寸)")

    print("=" * 50)

def modify_config():
    """修改配置 - 分类设置"""
    config = load_config()

    while True:
        print("\n🔧 设置菜单:")
        print("=" * 40)
        print("1. 📐 基础设置 (边缘像素、模型)")
        print("2. 💾 保存设置 (位置、格式、文件名)")
        print("3. 📁 批量处理设置")
        print("4. 🖥️ 显示设置")
        print("5. 📏 输出尺寸设置")
        print("6. 📊 查看当前设置")
        print("7. 🔄 重置为默认设置")
        print("8. ✅ 保存并退出")
        print("9. ❌ 取消")
        print("=" * 40)

        choice = input("请选择 (1-9): ").strip()

        if choice == "1":
            modify_basic_settings(config)
        elif choice == "2":
            modify_save_settings(config)
        elif choice == "3":
            modify_batch_settings(config)
        elif choice == "4":
            modify_display_settings(config)
        elif choice == "5":
            modify_resize_settings(config)
        elif choice == "6":
            show_current_config()
        elif choice == "7":
            if input("确认重置所有设置? (y/n): ").lower() == 'y':
                config = DEFAULT_CONFIG.copy()
                print("✅ 已重置为默认设置")
        elif choice == "8":
            if save_config(config):
                print("\n✅ 设置已保存")
                show_current_config()
                break
            else:
                print("\n❌ 设置保存失败")
        elif choice == "9":
            print("❌ 已取消，设置未保存")
            break
        else:
            print("❌ 无效选项")

def modify_basic_settings(config):
    """修改基础设置"""
    print("\n📐 基础设置:")
    print("-" * 30)

    # 边缘预留像素
    padding = input(f"边缘预留像素 (当前: {config['edge_padding']}, 0-100): ").strip()
    if padding and padding.isdigit() and 0 <= int(padding) <= 100:
        config['edge_padding'] = int(padding)
        print(f"✅ 边缘预留像素设为: {padding}")

    # 模型选择
    print(f"\n使用模型 (当前: {config['model']}):")
    print("1. u2net (快速，通用)")
    print("2. rmbg-1.4 (推荐，最佳效果)")
    print("3. isnet-general-use (平衡)")
    print("4. sam (Meta最强技术，较慢)")
    model_choice = input("选择模型 (1-4): ").strip()
    models = {"1": "u2net", "2": "rmbg-1.4", "3": "isnet-general-use", "4": "sam"}
    if model_choice in models:
        config['model'] = models[model_choice]
        print(f"✅ 模型设为: {models[model_choice]}")

def modify_save_settings(config):
    """修改保存设置"""
    print("\n💾 保存设置:")
    print("-" * 30)

    # 保存位置
    print(f"保存位置 (当前: {config['save_location']}):")
    print("1. 原图片同目录")
    print("2. 自定义目录")
    save_choice = input("选择保存位置 (1-2): ").strip()
    if save_choice == "1":
        config['save_location'] = "same_folder"
        print("✅ 保存位置设为: 原图片同目录")
    elif save_choice == "2":
        config['save_location'] = "custom"
        custom_path = input("输入自定义保存目录: ").strip().strip('"')
        if custom_path and os.path.exists(custom_path):
            config['custom_save_path'] = custom_path
            print(f"✅ 自定义保存目录设为: {custom_path}")
        else:
            print("⚠️ 目录不存在，保持原设置")

    # 文件名格式
    filename_format = input(f"文件名格式 (当前: {config['filename_format']}, 用{{name}}代表原文件名): ").strip()
    if filename_format:
        config['filename_format'] = filename_format
        print(f"✅ 文件名格式设为: {filename_format}")

    # 输出格式
    print(f"\n输出格式 (当前: {config['output_format']}):")
    print("1. PNG (推荐，支持透明)")
    print("2. JPG (较小文件)")
    format_choice = input("选择格式 (1-2): ").strip()
    if format_choice == "1":
        config['output_format'] = "png"
        print("✅ 输出格式设为: PNG")
    elif format_choice == "2":
        config['output_format'] = "jpg"
        print("✅ 输出格式设为: JPG")

        # JPG质量设置
        quality = input(f"JPG质量 (当前: {config['quality']}, 1-100): ").strip()
        if quality and quality.isdigit() and 1 <= int(quality) <= 100:
            config['quality'] = int(quality)
            print(f"✅ JPG质量设为: {quality}%")

def modify_batch_settings(config):
    """修改批量处理设置"""
    print("\n📁 批量处理设置:")
    print("-" * 30)

    # 批量处理子文件夹
    subfolder = input(f"批量处理时创建子文件夹? (当前: {'是' if config['batch_subfolder'] else '否'}) (y/n): ").strip().lower()
    if subfolder == 'y':
        config['batch_subfolder'] = True
        print("✅ 批量处理时将创建子文件夹")
    elif subfolder == 'n':
        config['batch_subfolder'] = False
        print("✅ 批量处理时不创建子文件夹")

    # 覆盖已存在文件
    overwrite = input(f"覆盖已存在的文件? (当前: {'是' if config['overwrite_existing'] else '否'}) (y/n): ").strip().lower()
    if overwrite == 'y':
        config['overwrite_existing'] = True
        print("✅ 将覆盖已存在的文件")
    elif overwrite == 'n':
        config['overwrite_existing'] = False
        print("✅ 不覆盖已存在的文件")

def modify_display_settings(config):
    """修改显示设置"""
    print("\n🖥️ 显示设置:")
    print("-" * 30)

    # 显示进度
    progress = input(f"显示处理进度? (当前: {'是' if config['show_progress'] else '否'}) (y/n): ").strip().lower()
    if progress == 'y':
        config['show_progress'] = True
        print("✅ 将显示处理进度")
    elif progress == 'n':
        config['show_progress'] = False
        print("✅ 不显示处理进度")

    # 自动打开结果
    auto_open = input(f"处理完自动打开结果文件夹? (当前: {'是' if config['auto_open_result'] else '否'}) (y/n): ").strip().lower()
    if auto_open == 'y':
        config['auto_open_result'] = True
        print("✅ 处理完将自动打开结果文件夹")
    elif auto_open == 'n':
        config['auto_open_result'] = False
        print("✅ 处理完不自动打开结果文件夹")

def modify_resize_settings(config):
    """修改输出尺寸设置"""
    print("\n📏 输出尺寸设置:")
    print("-" * 30)

    # 是否调整输出尺寸
    resize = input(f"调整输出尺寸? (当前: {'是' if config['resize_output'] else '否'}) (y/n): ").strip().lower()
    if resize == 'y':
        config['resize_output'] = True
        print("✅ 将调整输出尺寸")

        # 最大宽度
        max_width = input(f"最大宽度 (当前: {config['max_width']}): ").strip()
        if max_width and max_width.isdigit() and int(max_width) > 0:
            config['max_width'] = int(max_width)
            print(f"✅ 最大宽度设为: {max_width}")

        # 最大高度
        max_height = input(f"最大高度 (当前: {config['max_height']}): ").strip()
        if max_height and max_height.isdigit() and int(max_height) > 0:
            config['max_height'] = int(max_height)
            print(f"✅ 最大高度设为: {max_height}")

    elif resize == 'n':
        config['resize_output'] = False
        print("✅ 保持原始尺寸")

# 预设配置
PRESETS = {
    "快速": {
        "edge_padding": 10,
        "model": "u2net",
        "output_format": "jpg",
        "quality": 85,
        "show_progress": False,
        "resize_output": True,
        "max_width": 1280,
        "max_height": 720
    },
    "高质量": {
        "edge_padding": 20,
        "model": "rmbg-1.4",
        "output_format": "png",
        "show_progress": True,
        "resize_output": False
    },
    "最强": {
        "edge_padding": 25,
        "model": "sam",
        "output_format": "png",
        "show_progress": True,
        "resize_output": False
    },
    "批量": {
        "edge_padding": 15,
        "model": "rmbg-1.4",
        "output_format": "jpg",
        "quality": 90,
        "batch_subfolder": True,
        "show_progress": True,
        "auto_open_result": True
    }
}

def apply_preset(preset_name):
    """应用预设配置"""
    if preset_name not in PRESETS:
        return False

    config = load_config()
    preset = PRESETS[preset_name]

    # 应用预设，但保留用户的自定义路径等设置
    for key, value in preset.items():
        config[key] = value

    return save_config(config)

def show_presets():
    """显示可用预设"""
    print("\n🎯 预设配置:")
    print("=" * 50)
    for name, preset in PRESETS.items():
        print(f"📋 {name}:")
        print(f"  模型: {preset['model']}")
        print(f"  边缘: {preset['edge_padding']}像素")
        print(f"  格式: {preset['output_format'].upper()}")
        if 'quality' in preset:
            print(f"  质量: {preset['quality']}%")
        print()

if __name__ == "__main__":
    show_current_config()
