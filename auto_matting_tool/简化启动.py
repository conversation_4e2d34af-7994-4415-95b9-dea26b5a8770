#!/usr/bin/env python3
"""
简化启动抠图工具 - 只使用RMBG-1.4模型
"""

import os
import sys
from config import load_config, show_current_config, modify_config, get_output_path

def process_single_image():
    """单张图片处理"""
    print("\n🖼️ 单张图片处理")
    config = load_config()
    
    print("💡 提示：可以直接拖拽图片文件到终端窗口来输入路径")
    input_path = input("输入图片路径: ").strip()

    # 处理路径中的特殊字符
    if input_path.startswith('"') and input_path.endswith('"'):
        input_path = input_path[1:-1]  # 去掉首尾引号

    # 处理转义字符
    input_path = input_path.replace('\\ ', ' ')  # 处理转义的空格
    input_path = input_path.replace('\\(', '(')  # 处理转义的左括号
    input_path = input_path.replace('\\)', ')')  # 处理转义的右括号

    if not input_path:
        print("❌ 输入路径不能为空")
        return

    print(f"📝 处理后的路径: {input_path}")

    # 检查文件是否存在
    if not os.path.exists(input_path):
        print(f"❌ 文件不存在: {input_path}")
        print("💡 请检查:")
        print("  1. 文件路径是否正确")
        print("  2. 文件是否被移动或删除")
        print("  3. 尝试重新拖拽文件到终端")
        print(f"  4. 原始输入: {repr(input('再次确认，请重新输入路径: ').strip())}")
        return
    
    # 使用配置生成输出路径
    output_path = get_output_path(input_path, config)
    
    print(f"🎛️ 使用设置: {config['edge_padding']}像素边缘 | RMBG-1.4模型")
    print(f"📁 输出到: {output_path}")
    
    # 获取脚本目录
    script_dir = os.path.dirname(os.path.abspath(__file__))
    
    cmd = f"cd '{script_dir}' && python3 auto_matting.py -i '{input_path}' -o '{output_path}' -p {config['edge_padding']} -m rmbg-1.4"
    print("⏳ 处理中...")
    print(f"🔧 执行命令: {cmd}")
    result = os.system(cmd)

    if result == 0:
        print("✅ 处理完成!")
        if config['auto_open_result']:
            import subprocess
            subprocess.run(['open', os.path.dirname(output_path)])
    else:
        print("❌ 处理失败")
        print("💡 可能的原因:")
        print("  1. 图片格式不支持")
        print("  2. 路径包含特殊字符")
        print("  3. 依赖包未安装")

def process_batch():
    """批量处理"""
    print("\n📁 批量处理")
    config = load_config()
    
    print("💡 提示：可以直接拖拽文件夹到终端窗口来输入路径")
    input_path = input("输入图片文件夹路径: ").strip()

    # 处理路径中的特殊字符
    if input_path.startswith('"') and input_path.endswith('"'):
        input_path = input_path[1:-1]  # 去掉首尾引号

    # 处理转义字符
    input_path = input_path.replace('\\ ', ' ')  # 处理转义的空格
    input_path = input_path.replace('\\(', '(')  # 处理转义的左括号
    input_path = input_path.replace('\\)', ')')  # 处理转义的右括号

    if not input_path:
        print("❌ 输入路径不能为空")
        return

    print(f"📝 处理后的路径: {input_path}")

    # 检查文件夹是否存在
    if not os.path.exists(input_path):
        print(f"❌ 文件夹不存在: {input_path}")
        print("💡 请检查路径是否正确，或重新拖拽文件夹到终端")
        return
    
    # 生成输出路径
    if config['save_location'] == 'same_folder':
        output_path = f"{input_path}_抠图结果"
    else:
        custom_path = config['custom_save_path']
        if custom_path and os.path.exists(custom_path):
            import os.path
            folder_name = os.path.basename(input_path)
            output_path = os.path.join(custom_path, f"{folder_name}_抠图结果")
        else:
            output_path = f"{input_path}_抠图结果"
    
    print(f"🎛️ 使用设置: {config['edge_padding']}像素边缘 | RMBG-1.4模型")
    print(f"📁 输出到: {output_path}")
    
    # 获取脚本目录
    script_dir = os.path.dirname(os.path.abspath(__file__))
    
    # 先检查输入目录中是否有图片文件
    import glob
    supported_formats = ['*.jpg', '*.jpeg', '*.png', '*.bmp', '*.tiff', '*.webp', '*.JPG', '*.JPEG', '*.PNG', '*.BMP', '*.TIFF', '*.WEBP']
    image_files = []
    for pattern in supported_formats:
        image_files.extend(glob.glob(os.path.join(input_path, pattern)))

    if not image_files:
        print(f"❌ 在目录中没有找到支持的图片文件: {input_path}")
        print("💡 支持的格式: jpg, jpeg, png, bmp, tiff, webp")
        return

    print(f"📊 找到 {len(image_files)} 张图片")

    cmd = f"cd '{script_dir}' && python3 auto_matting.py -i '{input_path}' -o '{output_path}' -p {config['edge_padding']} -m rmbg-1.4"
    print("⏳ 处理中...")
    print(f"🔧 执行命令: {cmd}")
    result = os.system(cmd)

    if result == 0:
        print("✅ 批量处理完成!")
        if config['auto_open_result']:
            import subprocess
            subprocess.run(['open', output_path])
    else:
        print("❌ 处理失败")
        print("💡 可能的原因:")
        print("  1. 路径包含特殊字符")
        print("  2. 权限不足")
        print("  3. 依赖包未安装")

def main():
    print("🎯 抠图工具 - RMBG-1.4")
    print("=" * 40)
    
    # 检查核心文件
    script_dir = os.path.dirname(os.path.abspath(__file__))
    core_files = ["auto_matting.py", "config.py"]
    for file in core_files:
        if not os.path.exists(os.path.join(script_dir, file)):
            print(f"❌ 缺少核心文件: {file}")
            return False
    
    print("✅ 核心文件检查完成")
    
    # 显示当前配置
    config = load_config()
    print(f"\n⚙️ 当前设置: {config['edge_padding']}像素边缘 | RMBG-1.4模型 | 保存到{'同目录' if config['save_location'] == 'same_folder' else '自定义目录'}")
    
    # 显示选项
    print("\n请选择操作:")
    print("1. 🖼️ 单张图片处理 (RMBG-1.4)")
    print("2. 📁 批量处理 (RMBG-1.4)")
    print("3. 🧠 SAM-B 抠图 (Meta最强技术)")
    print("4. 📖 查看使用说明")
    print("5. 🔧 安装/检查依赖")
    print("6. ⚙️ 设置")

    choice = input("\n请输入选项 (1-6): ").strip()
    
    if choice == "1":
        process_single_image()
        
    elif choice == "2":
        process_batch()
        
    elif choice == "3":
        # SAM-B 抠图
        print("\n🧠 启动 SAM-B 抠图工具...")
        print("Meta最强分割技术 - 375MB模型")
        os.system(f"cd '{script_dir}' && python3 SAM抠图.py")

    elif choice == "4":
        # 查看说明
        print("\n📖 使用说明文档:")
        print("1. 快速使用指南.md")
        print("2. README.md (详细说明)")
        print("3. 模型推荐与对比.md")

        doc_choice = input("选择要查看的文档 (1-3): ").strip()
        doc_files = {
            "1": "快速使用指南.md",
            "2": "README.md",
            "3": "模型推荐与对比.md"
        }

        doc_file = doc_files.get(doc_choice, "快速使用指南.md")
        doc_path = os.path.join(script_dir, doc_file)

        if os.path.exists(doc_path):
            print(f"\n📖 {doc_file}:")
            print("-" * 50)
            with open(doc_path, 'r', encoding='utf-8') as f:
                content = f.read()
                # 只显示前50行
                lines = content.split('\n')
                if len(lines) > 50:
                    print('\n'.join(lines[:50]))
                    print(f"\n... (还有 {len(lines)-50} 行，请直接打开文件查看完整内容)")
                else:
                    print(content)
        else:
            print(f"❌ 找不到文档文件: {doc_file}")

    elif choice == "5":
        # 安装依赖
        print("\n🔧 检查并安装依赖...")
        os.system("python3 -m pip install rembg[new] opencv-python pillow numpy onnxruntime")
        print("✅ 依赖安装完成")

    elif choice == "6":
        # 设置
        print("\n⚙️ 设置")
        show_current_config()
        print("\n1. 查看当前设置")
        print("2. 修改设置")
        setting_choice = input("请选择 (1-2): ").strip()

        if setting_choice == "2":
            modify_config()
        
    else:
        print("❌ 无效选项")
    
    # 自动继续，不再询问
    print("\n" + "="*50)
    print("✅ 操作完成！可以继续拖拽图片进行处理...")
    print("💡 提示: 按 Ctrl+C 或直接关闭终端退出")
    return True

def run():
    """主运行循环"""
    try:
        while True:
            if not main():
                break
    except KeyboardInterrupt:
        print("\n👋 再见!")
        sys.exit(0)

if __name__ == "__main__":
    run()
