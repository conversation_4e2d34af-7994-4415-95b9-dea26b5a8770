# 🎯 抠图工具 - 快速使用指南

## 🚀 最简单的使用方法

### 1. 启动工具
**双击** `抠图工具.app` 即可启动

### 2. 选择操作
启动后会看到菜单：
```
请选择操作:
1. 🚀 快速批量处理（推荐）
2. 🎛️ 自定义参数处理
3. 📖 查看使用说明
4. 🔧 安装/检查依赖
```

### 3. 处理图片
- **选择1**：自动处理当前目录的所有图片
- **选择2**：自定义输入/输出路径和参数

## 📁 文件准备

### 输入图片
把要处理的图片放在任意文件夹中，支持格式：
- JPG / JPEG
- PNG  
- WEBP
- BMP
- TIFF

### 输出结果
- 自动生成PNG格式（支持透明背景）
- 文件名格式：`原文件名_matted.png`

## 🎛️ 边缘预留设置

| 像素数 | 效果 | 适用场景 |
|--------|------|----------|
| 5-10   | 精细抠图 | 需要干净边缘 |
| 15     | 标准预留 | 大部分情况（默认）|
| 20-30  | 宽松抠图 | 保留更多背景 |

## 💡 使用技巧

### 批量处理
1. 把所有图片放在一个文件夹
2. 启动工具选择"快速批量处理"
3. 等待处理完成

### 自定义处理
1. 启动工具选择"自定义参数处理"
2. 输入图片路径（文件或文件夹）
3. 输入输出路径
4. 设置边缘预留像素数

## 🔧 常见问题

**Q: 第一次使用提示缺少依赖？**
A: 选择菜单中的"安装/检查依赖"，会自动安装

**Q: 处理速度慢？**
A: 正常现象，AI处理需要时间，平均每张8-10秒

**Q: 想要不同的边缘效果？**
A: 使用"自定义参数处理"，调整边缘预留像素数

**Q: 支持哪些图片格式？**
A: 输入支持JPG/PNG/WEBP等，输出固定为PNG格式

## 🎯 示例操作

### 处理单张图片
```
选择: 2 (自定义参数处理)
输入图片路径: /Users/<USER>/Desktop/照片.jpg
输出路径: /Users/<USER>/Desktop/抠图结果.png
边缘预留像素: 15
```

### 批量处理文件夹
```
选择: 2 (自定义参数处理)  
输入图片路径: /Users/<USER>/Desktop/照片文件夹
输出路径: /Users/<USER>/Desktop/抠图结果文件夹
边缘预留像素: 20
```

---

🎉 **现在就开始使用吧！双击 `抠图工具.app` 即可开始！**
