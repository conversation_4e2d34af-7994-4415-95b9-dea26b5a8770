#!/usr/bin/env python3
"""
SAM-B 抠图工具 - Meta最强技术
"""

import os
import sys
from config import load_config, get_output_path, save_config

def process_single_image_sam():
    """使用SAM-B模型处理单张图片"""
    print("\n🧠 SAM-B 单张图片处理")
    print("Meta最强分割技术 - 375MB模型")
    config = load_config()
    
    print("💡 提示：可以直接拖拽图片文件到终端窗口来输入路径")
    input_path = input("输入图片路径: ").strip()
    
    # 处理路径中的特殊字符
    if input_path.startswith('"') and input_path.endswith('"'):
        input_path = input_path[1:-1]  # 去掉首尾引号
    
    # 处理转义字符
    input_path = input_path.replace('\\ ', ' ')  # 处理转义的空格
    input_path = input_path.replace('\\(', '(')  # 处理转义的左括号
    input_path = input_path.replace('\\)', ')')  # 处理转义的右括号
    
    if not input_path:
        print("❌ 输入路径不能为空")
        return
        
    print(f"📝 处理后的路径: {input_path}")
        
    # 检查文件是否存在
    if not os.path.exists(input_path):
        print(f"❌ 文件不存在: {input_path}")
        print("💡 请检查路径是否正确，或重新拖拽文件到终端")
        return
    
    # 使用配置生成输出路径，但文件名加上SAM标识
    output_path = get_output_path(input_path, config)
    # 在文件名中加入SAM标识
    from pathlib import Path
    output_file = Path(output_path)
    output_path = str(output_file.parent / f"{output_file.stem}_SAM.png")
    
    print(f"🎛️ 使用设置: {config['edge_padding']}像素边缘 | SAM-B模型")
    print(f"📁 输出到: {output_path}")
    
    # 获取脚本目录
    script_dir = os.path.dirname(os.path.abspath(__file__))
    
    cmd = f"cd '{script_dir}' && python3 auto_matting.py -i '{input_path}' -o '{output_path}' -p {config['edge_padding']} -m sam"
    print("⏳ 处理中... (首次使用会下载375MB模型)")
    result = os.system(cmd)
    
    if result == 0:
        print("✅ SAM-B 处理完成!")
        if config['auto_open_result']:
            import subprocess
            subprocess.run(['open', os.path.dirname(output_path)])
    else:
        print("❌ 处理失败")

def process_batch_sam():
    """使用SAM-B模型批量处理"""
    print("\n📁 SAM-B 批量处理")
    print("Meta最强分割技术 - 375MB模型")
    config = load_config()

    print("💡 提示：可以直接拖拽文件夹到终端窗口来输入路径")
    input_path = input("输入图片文件夹路径: ").strip()

    # 处理路径中的特殊字符
    if input_path.startswith('"') and input_path.endswith('"'):
        input_path = input_path[1:-1]  # 去掉首尾引号

    # 处理转义字符
    input_path = input_path.replace('\\ ', ' ')  # 处理转义的空格
    input_path = input_path.replace('\\(', '(')  # 处理转义的左括号
    input_path = input_path.replace('\\)', ')')  # 处理转义的右括号

    if not input_path:
        print("❌ 输入路径不能为空")
        return

    print(f"📝 处理后的路径: {input_path}")

    # 检查文件夹是否存在
    if not os.path.exists(input_path):
        print(f"❌ 文件夹不存在: {input_path}")
        print("💡 请检查路径是否正确，或重新拖拽文件夹到终端")
        return

    # 生成输出路径，加上SAM标识
    if config['save_location'] == 'same_folder':
        output_path = f"{input_path}_SAM抠图结果"
    else:
        custom_path = config['custom_save_path']
        if custom_path and os.path.exists(custom_path):
            import os.path
            folder_name = os.path.basename(input_path)
            output_path = os.path.join(custom_path, f"{folder_name}_SAM抠图结果")
        else:
            output_path = f"{input_path}_SAM抠图结果"

    print(f"🎛️ 使用设置: {config['edge_padding']}像素边缘 | SAM-B模型")
    print(f"📁 输出到: {output_path}")

    # 获取脚本目录
    script_dir = os.path.dirname(os.path.abspath(__file__))

    cmd = f"cd '{script_dir}' && python3 auto_matting.py -i '{input_path}' -o '{output_path}' -p {config['edge_padding']} -m sam"
    print("⏳ 批量处理中... (首次使用会下载375MB模型)")
    result = os.system(cmd)

    if result == 0:
        print("✅ SAM-B 批量处理完成!")
        if config['auto_open_result']:
            import subprocess
            subprocess.run(['open', output_path])
    else:
        print("❌ 处理失败")

def show_current_config():
    """显示当前配置"""
    config = load_config()
    print("\n📋 当前 SAM-B 设置:")
    print(f"  🎯 边缘预留: {config['edge_padding']} 像素")
    print(f"  🤖 模型: SAM-B (Meta最强技术)")
    print(f"  📁 保存位置: {'同目录' if config['save_location'] == 'same_folder' else '自定义目录'}")
    if config['save_location'] == 'custom':
        print(f"  📂 自定义路径: {config['custom_save_path']}")
    print(f"  📝 文件名格式: {config['filename_format']}")
    print(f"  🖼️ 输出格式: {config['output_format'].upper()}")
    print(f"  🚀 自动打开结果: {'是' if config['auto_open_result'] else '否'}")

def modify_config():
    """修改配置"""
    config = load_config()

    print("\n⚙️ SAM-B 设置修改")
    print("直接回车保持当前设置")

    # 边缘预留
    current_padding = config['edge_padding']
    padding_input = input(f"边缘预留像素 (当前: {current_padding}): ").strip()
    if padding_input:
        try:
            config['edge_padding'] = int(padding_input)
        except ValueError:
            print("❌ 无效数字，保持原设置")

    # 保存位置
    current_location = config['save_location']
    location_text = "同目录" if current_location == 'same_folder' else "自定义目录"
    print(f"\n保存位置 (当前: {location_text})")
    print("1. 同目录")
    print("2. 自定义目录")
    location_choice = input("选择 (1-2): ").strip()

    if location_choice == "1":
        config['save_location'] = 'same_folder'
    elif location_choice == "2":
        config['save_location'] = 'custom'
        custom_path = input("输入自定义保存路径: ").strip()
        if custom_path and os.path.exists(custom_path):
            config['custom_save_path'] = custom_path
        else:
            print("❌ 路径无效，保持原设置")

    # 文件名格式
    current_format = config['filename_format']
    format_input = input(f"文件名格式 (当前: {current_format}): ").strip()
    if format_input:
        config['filename_format'] = format_input

    # 输出格式
    current_output = config['output_format']
    print(f"\n输出格式 (当前: {current_output.upper()})")
    print("1. PNG (推荐)")
    print("2. JPG")
    format_choice = input("选择 (1-2): ").strip()

    if format_choice == "1":
        config['output_format'] = 'png'
    elif format_choice == "2":
        config['output_format'] = 'jpg'

    # 自动打开结果
    current_auto_open = config['auto_open_result']
    auto_open_text = "是" if current_auto_open else "否"
    print(f"\n处理完成后自动打开结果文件夹 (当前: {auto_open_text})")
    print("1. 是")
    print("2. 否")
    auto_choice = input("选择 (1-2): ").strip()

    if auto_choice == "1":
        config['auto_open_result'] = True
    elif auto_choice == "2":
        config['auto_open_result'] = False

    # 保存配置
    if save_config(config):
        print("✅ SAM-B 设置已保存")
    else:
        print("❌ 设置保存失败")

def main():
    print("🧠 SAM-B 抠图工具")
    print("Meta最强分割技术")
    print("=" * 40)
    
    # 检查核心文件
    script_dir = os.path.dirname(os.path.abspath(__file__))
    core_files = ["auto_matting.py", "config.py"]
    for file in core_files:
        if not os.path.exists(os.path.join(script_dir, file)):
            print(f"❌ 缺少核心文件: {file}")
            return False
    
    print("✅ 核心文件检查完成")
    
    # 显示当前配置
    config = load_config()
    print(f"\n⚙️ 当前设置: {config['edge_padding']}像素边缘 | SAM-B模型 | 保存到{'同目录' if config['save_location'] == 'same_folder' else '自定义目录'}")
    
    # 显示选项
    print("\n请选择操作:")
    print("1. 🖼️ 单张图片处理")
    print("2. 📁 批量处理")
    print("3. ⚙️ SAM-B 设置")
    print("4. 🔙 返回主程序")

    choice = input("\n请输入选项 (1-4): ").strip()
    
    if choice == "1":
        process_single_image_sam()
        
    elif choice == "2":
        process_batch_sam()
        
    elif choice == "3":
        # SAM-B 设置
        print("\n⚙️ SAM-B 设置")
        show_current_config()
        print("\n1. 查看当前设置")
        print("2. 修改设置")
        setting_choice = input("请选择 (1-2): ").strip()

        if setting_choice == "2":
            modify_config()

    elif choice == "4":
        # 启动主程序
        os.system(f"cd '{script_dir}' && python3 简化启动.py")
        return False
        
    else:
        print("❌ 无效选项")
    
    # 询问是否继续
    print("\n" + "="*50)
    continue_choice = input("继续使用SAM-B? (回车继续, q退出, m返回主程序): ").strip().lower()
    if continue_choice == 'q':
        print("👋 再见!")
        return False
    elif continue_choice == 'm':
        os.system(f"cd '{script_dir}' && python3 简化启动.py")
        return False
    return True

def run():
    """主运行循环"""
    while True:
        if not main():
            break

if __name__ == "__main__":
    run()
