#!/usr/bin/env python3
"""
传统算法人像追色工具 - LAB色彩空间 + 肤色检测
专门针对人像肤色进行色彩匹配，保持背景不变
"""

import cv2
import numpy as np
from PIL import Image
import argparse
import os
from skimage import color
import warnings
warnings.filterwarnings('ignore')

class SkinColorTransfer:
    """基于肤色检测的色彩迁移"""
    
    def __init__(self):
        self.skin_lower = np.array([0, 20, 70], dtype=np.uint8)    # HSV肤色下限
        self.skin_upper = np.array([20, 255, 255], dtype=np.uint8) # HSV肤色上限
        
    def detect_skin_hsv(self, image):
        """使用HSV色彩空间检测肤色区域"""
        # 转换到HSV色彩空间
        hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)
        
        # 创建肤色掩码
        mask = cv2.inRange(hsv, self.skin_lower, self.skin_upper)
        
        # 形态学操作，去除噪声
        kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (11, 11))
        mask = cv2.morphologyEx(mask, cv2.MORPH_OPEN, kernel)
        mask = cv2.morphologyEx(mask, cv2.MORPH_CLOSE, kernel)
        
        # 高斯模糊，使边缘更自然
        mask = cv2.GaussianBlur(mask, (3, 3), 0)
        
        return mask
    
    def detect_skin_ycrcb(self, image):
        """使用YCrCb色彩空间检测肤色区域（更准确）"""
        # 转换到YCrCb色彩空间
        ycrcb = cv2.cvtColor(image, cv2.COLOR_BGR2YCrCb)
        
        # YCrCb肤色范围（更准确的肤色检测）
        lower_skin = np.array([0, 135, 85], dtype=np.uint8)
        upper_skin = np.array([255, 180, 135], dtype=np.uint8)
        
        mask = cv2.inRange(ycrcb, lower_skin, upper_skin)
        
        # 形态学操作
        kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (11, 11))
        mask = cv2.morphologyEx(mask, cv2.MORPH_OPEN, kernel)
        mask = cv2.morphologyEx(mask, cv2.MORPH_CLOSE, kernel)
        
        # 高斯模糊
        mask = cv2.GaussianBlur(mask, (3, 3), 0)
        
        return mask
    
    def combine_skin_masks(self, image):
        """结合多种方法检测肤色，提高准确性"""
        mask_hsv = self.detect_skin_hsv(image)
        mask_ycrcb = self.detect_skin_ycrcb(image)
        
        # 取两个掩码的交集，提高准确性
        combined_mask = cv2.bitwise_and(mask_hsv, mask_ycrcb)
        
        # 如果交集太小，使用并集
        if np.sum(combined_mask) < np.sum(mask_hsv) * 0.3:
            combined_mask = cv2.bitwise_or(mask_hsv, mask_ycrcb)
        
        return combined_mask
    
    def lab_color_transfer(self, source, target, mask=None, transfer_strength=0.6):
        """在LAB色彩空间进行温和的色彩调整"""
        # 确保两个图像尺寸一致
        if source.shape[:2] != target.shape[:2]:
            source = cv2.resize(source, (target.shape[1], target.shape[0]))

        # 转换到LAB色彩空间
        source_lab = cv2.cvtColor(source, cv2.COLOR_BGR2LAB).astype(np.float32)
        target_lab = cv2.cvtColor(target, cv2.COLOR_BGR2LAB).astype(np.float32)

        # 如果有掩码，只处理掩码区域
        if mask is not None:
            # 确保掩码是单通道
            if len(mask.shape) == 3:
                mask = cv2.cvtColor(mask, cv2.COLOR_BGR2GRAY)

            # 确保掩码尺寸与图像匹配
            if mask.shape[:2] != target.shape[:2]:
                mask = cv2.resize(mask, (target.shape[1], target.shape[0]))

            # 将掩码转换为3通道并归一化
            mask_3d = np.stack([mask, mask, mask], axis=2) / 255.0

            # 计算掩码区域的统计信息
            source_mean = []
            target_mean = []

            for i in range(3):  # L, A, B三个通道
                source_channel = source_lab[:, :, i]
                target_channel = target_lab[:, :, i]
                mask_channel = mask / 255.0

                # 只计算掩码区域的统计信息
                if np.sum(mask_channel) > 0:
                    mask_bool = mask_channel > 0.5
                    source_masked = source_channel[mask_bool]
                    target_masked = target_channel[mask_bool]

                    source_mean.append(np.mean(source_masked))
                    target_mean.append(np.mean(target_masked))
                else:
                    # 如果掩码为空，使用全图统计
                    source_mean.append(np.mean(source_channel))
                    target_mean.append(np.mean(target_channel))

            # 进行温和的色彩调整
            result_lab = target_lab.copy()

            for i in range(3):
                # 计算色彩差异
                color_diff = source_mean[i] - target_mean[i]

                # 应用温和的调整（只调整一部分差异）
                if i == 0:  # L通道（亮度）- 更保守的调整
                    adjustment = color_diff * transfer_strength * 0.3
                else:  # A, B通道（色彩）- 稍微积极一些
                    adjustment = color_diff * transfer_strength * 0.7

                # 只在掩码区域应用调整
                adjusted_channel = target_lab[:, :, i] + adjustment
                result_lab[:, :, i] = target_lab[:, :, i] * (1 - mask_3d[:, :, i]) + adjusted_channel * mask_3d[:, :, i]
        else:
            # 全图色彩调整
            source_mean = np.mean(source_lab.reshape(-1, 3), axis=0)
            target_mean = np.mean(target_lab.reshape(-1, 3), axis=0)

            result_lab = target_lab.copy()
            for i in range(3):
                color_diff = source_mean[i] - target_mean[i]
                if i == 0:  # L通道
                    adjustment = color_diff * transfer_strength * 0.3
                else:  # A, B通道
                    adjustment = color_diff * transfer_strength * 0.7
                result_lab[:, :, i] = target_lab[:, :, i] + adjustment

        # 确保值在有效范围内
        result_lab = np.clip(result_lab, 0, 255)

        # 转换回BGR
        result_bgr = cv2.cvtColor(result_lab.astype(np.uint8), cv2.COLOR_LAB2BGR)

        return result_bgr
    
    def transfer_skin_color(self, target_path, reference_path, output_dir="results", transfer_strength=0.6):
        """执行肤色色彩调整"""
        print(f"🎨 开始肤色色彩调整...")
        print(f"📸 目标图片: {os.path.basename(target_path)}")
        print(f"🎯 参考图片: {os.path.basename(reference_path)}")
        print(f"💪 调整强度: {transfer_strength:.1f}")

        # 读取图像
        target_img = cv2.imread(target_path)
        reference_img = cv2.imread(reference_path)

        if target_img is None:
            print(f"❌ 无法读取目标图片: {target_path}")
            return False

        if reference_img is None:
            print(f"❌ 无法读取参考图片: {reference_path}")
            return False

        # 检测目标图像的肤色区域
        print("🔍 检测肤色区域...")
        skin_mask = self.combine_skin_masks(target_img)

        # 检查是否检测到肤色
        skin_ratio = np.sum(skin_mask > 0) / (skin_mask.shape[0] * skin_mask.shape[1])
        print(f"📊 肤色区域占比: {skin_ratio:.2%}")

        if skin_ratio < 0.01:  # 如果肤色区域太小
            print("⚠️ 检测到的肤色区域较小，将使用全图调整")
            skin_mask = None

        # 执行色彩调整
        print("🎨 执行温和的色彩调整...")
        result_img = self.lab_color_transfer(reference_img, target_img, skin_mask, transfer_strength)
        
        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)
        
        # 保存结果
        base_name = os.path.splitext(os.path.basename(target_path))[0]
        
        # 保存BGR结果为JPG（白色背景）
        jpg_path = os.path.join(output_dir, f"{base_name}_skin_matched.jpg")
        cv2.imwrite(jpg_path, result_img)
        
        # 转换为RGB并保存为PNG
        result_rgb = cv2.cvtColor(result_img, cv2.COLOR_BGR2RGB)
        result_pil = Image.fromarray(result_rgb)
        png_path = os.path.join(output_dir, f"{base_name}_skin_matched.png")
        result_pil.save(png_path)
        
        print(f"✅ 处理完成!")
        print(f"📁 JPG结果: {jpg_path}")
        print(f"📁 PNG结果: {png_path}")
        
        # 可选：保存肤色掩码用于调试
        if skin_mask is not None:
            mask_path = os.path.join(output_dir, f"{base_name}_skin_mask.jpg")
            cv2.imwrite(mask_path, skin_mask)
            print(f"🔍 肤色掩码: {mask_path}")
        
        return True

def main():
    parser = argparse.ArgumentParser(description='传统算法人像肤色调整工具')
    parser.add_argument('--target', '-t', required=True, help='目标图片路径')
    parser.add_argument('--reference', '-r', required=True, help='参考图片路径')
    parser.add_argument('--output', '-o', default='results', help='输出目录')
    parser.add_argument('--strength', '-s', type=float, default=0.6, help='调整强度 (0.0-1.0)')

    args = parser.parse_args()

    # 检查输入文件
    if not os.path.exists(args.target):
        print(f"❌ 目标图片不存在: {args.target}")
        return

    if not os.path.exists(args.reference):
        print(f"❌ 参考图片不存在: {args.reference}")
        return

    # 检查强度参数
    if not 0.0 <= args.strength <= 1.0:
        print(f"❌ 调整强度必须在0.0-1.0之间，当前值: {args.strength}")
        return

    # 创建处理器
    processor = SkinColorTransfer()

    # 执行处理
    success = processor.transfer_skin_color(args.target, args.reference, args.output, args.strength)

    if success:
        print("🎉 处理成功完成!")
    else:
        print("💥 处理失败")

if __name__ == "__main__":
    main()
