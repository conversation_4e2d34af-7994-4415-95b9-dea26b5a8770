#!/bin/bash
# 人像追色工具快捷启动脚本 (macOS)

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

echo "🎨 人像追色工具启动器"
echo "=" * 40

# 检查Python3是否安装
if ! command -v python3 &> /dev/null; then
    echo "❌ 未找到Python3，请先安装Python"
    echo "💡 建议安装方式:"
    echo "   1. 从官网下载: https://www.python.org/downloads/"
    echo "   2. 使用Homebrew: brew install python3"
    read -p "按回车键退出..."
    exit 1
fi

echo "✅ 找到Python3: $(python3 --version)"

# 检查依赖是否安装
echo "🔍 检查依赖包..."
python3 -c "import cv2, numpy, PIL, skimage" 2>/dev/null
if [ $? -ne 0 ]; then
    echo "⚠️ 依赖包未安装，正在自动安装..."
    python3 install_deps.py
    if [ $? -ne 0 ]; then
        echo "❌ 依赖安装失败"
        read -p "按回车键退出..."
        exit 1
    fi
else
    echo "✅ 依赖包已安装"
fi

# 启动演示程序
echo ""
echo "🚀 启动人像追色工具..."
python3 demo_skin_transfer.py

# 保持窗口打开
read -p "按回车键退出..."
