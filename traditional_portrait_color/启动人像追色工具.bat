@echo off
chcp 65001 >nul
title 人像追色工具启动器

echo 🎨 人像追色工具启动器
echo ========================================

REM 切换到脚本所在目录
cd /d "%~dp0"

REM 检查Python3是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 未找到Python，请先安装Python
    echo 💡 建议安装方式:
    echo    从官网下载: https://www.python.org/downloads/
    echo    安装时记得勾选 "Add Python to PATH"
    pause
    exit /b 1
)

echo ✅ 找到Python
python --version

REM 检查依赖是否安装
echo 🔍 检查依赖包...
python -c "import cv2, numpy, PIL, skimage" >nul 2>&1
if errorlevel 1 (
    echo ⚠️ 依赖包未安装，正在自动安装...
    python install_deps.py
    if errorlevel 1 (
        echo ❌ 依赖安装失败
        pause
        exit /b 1
    )
) else (
    echo ✅ 依赖包已安装
)

REM 启动演示程序
echo.
echo 🚀 启动人像追色工具...
python demo_skin_transfer.py

REM 保持窗口打开
pause
