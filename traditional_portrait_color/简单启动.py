#!/usr/bin/env python3
"""
人像追色工具 - 简单启动器
双击即可运行，自动处理依赖和启动
"""

import os
import sys
import subprocess

def check_and_install_dependencies():
    """检查并安装依赖"""
    print("🔍 检查依赖包...")
    
    required_packages = ['cv2', 'numpy', 'PIL', 'skimage']
    missing_packages = []
    
    for package in required_packages:
        try:
            if package == 'cv2':
                import cv2
            elif package == 'numpy':
                import numpy
            elif package == 'PIL':
                from PIL import Image
            elif package == 'skimage':
                from skimage import color
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print(f"⚠️ 缺少依赖包: {', '.join(missing_packages)}")
        print("🔧 正在自动安装...")
        
        # 运行安装脚本
        try:
            result = subprocess.run([sys.executable, 'install_deps.py'], 
                                  capture_output=True, text=True)
            if result.returncode == 0:
                print("✅ 依赖安装成功!")
                return True
            else:
                print("❌ 依赖安装失败")
                print(result.stderr)
                return False
        except Exception as e:
            print(f"❌ 安装过程出错: {e}")
            return False
    else:
        print("✅ 所有依赖已安装")
        return True

def main():
    """主启动函数"""
    print("🎨 人像追色工具启动器")
    print("=" * 40)
    
    # 切换到脚本所在目录
    script_dir = os.path.dirname(os.path.abspath(__file__))
    os.chdir(script_dir)
    
    # 检查Python版本
    if sys.version_info < (3, 6):
        print("❌ 需要Python 3.6或更高版本")
        input("按回车键退出...")
        return
    
    print(f"✅ Python版本: {sys.version.split()[0]}")
    
    # 检查并安装依赖
    if not check_and_install_dependencies():
        print("💡 请手动安装依赖:")
        print("   pip install opencv-python pillow numpy scikit-image")
        input("按回车键退出...")
        return
    
    # 启动演示程序
    print("\n🚀 启动人像追色工具...")
    try:
        from demo_skin_transfer import interactive_demo
        interactive_demo()
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        print("\n💡 请尝试手动运行:")
        print("   python3 demo_skin_transfer.py")
        input("按回车键退出...")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n👋 用户取消，再见!")
    except Exception as e:
        print(f"\n💥 意外错误: {e}")
        input("按回车键退出...")
