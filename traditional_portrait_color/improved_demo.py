#!/usr/bin/env python3
"""
改进的肤色调整演示工具
生成多个不同强度的结果进行对比
"""

import os
import cv2
import numpy as np
from skin_color_transfer import SkinColorTransfer

def create_comparison_demo():
    """创建多强度对比演示"""
    print("🎨 改进的肤色调整演示")
    print("=" * 50)
    
    # 检查演示图片
    demo_dir = "demo_images"
    reference_path = os.path.join(demo_dir, "reference.jpg")
    target_path = os.path.join(demo_dir, "target.jpg")
    
    if not os.path.exists(reference_path):
        print(f"❌ 请准备参考图片: {reference_path}")
        return False
    
    if not os.path.exists(target_path):
        print(f"❌ 请准备目标图片: {target_path}")
        return False
    
    print(f"✅ 找到参考图片: {reference_path}")
    print(f"✅ 找到目标图片: {target_path}")
    
    # 创建处理器
    processor = SkinColorTransfer()
    
    # 不同的调整强度
    strengths = [0.0, 0.2, 0.4, 0.6, 0.8, 1.0]
    output_dir = "comparison_results"
    os.makedirs(output_dir, exist_ok=True)
    
    print(f"\n🔧 生成 {len(strengths)} 个不同强度的结果...")
    
    for strength in strengths:
        print(f"💪 处理强度 {strength:.1f}...")
        
        # 读取图像
        target_img = cv2.imread(target_path)
        reference_img = cv2.imread(reference_path)
        
        # 检测肤色
        skin_mask = processor.combine_skin_masks(target_img)
        
        # 执行调整
        result_img = processor.lab_color_transfer(reference_img, target_img, skin_mask, strength)
        
        # 保存结果
        output_name = f"strength_{strength:.1f}_result.jpg"
        output_path = os.path.join(output_dir, output_name)
        cv2.imwrite(output_path, result_img)
        
        print(f"   ✅ 保存: {output_path}")
    
    # 复制原图到结果目录用于对比
    original_output = os.path.join(output_dir, "0_original.jpg")
    target_img = cv2.imread(target_path)
    cv2.imwrite(original_output, target_img)
    
    reference_output = os.path.join(output_dir, "0_reference.jpg")
    reference_img = cv2.imread(reference_path)
    cv2.imwrite(reference_output, reference_img)
    
    print(f"\n🎉 对比演示完成!")
    print(f"📁 结果保存在: {output_dir}/")
    print("💡 查看不同强度的效果，选择最合适的参数")
    
    return True

def create_side_by_side_comparison():
    """创建并排对比图"""
    print("\n📊 创建并排对比图...")
    
    demo_dir = "demo_images"
    target_path = os.path.join(demo_dir, "target.jpg")
    reference_path = os.path.join(demo_dir, "reference.jpg")
    
    if not os.path.exists(target_path) or not os.path.exists(reference_path):
        print("❌ 缺少演示图片")
        return False
    
    # 读取图像
    target_img = cv2.imread(target_path)
    reference_img = cv2.imread(reference_path)
    
    # 调整参考图片尺寸
    reference_img = cv2.resize(reference_img, (target_img.shape[1], target_img.shape[0]))
    
    # 创建处理器
    processor = SkinColorTransfer()
    
    # 生成调整后的图像（使用中等强度）
    skin_mask = processor.combine_skin_masks(target_img)
    adjusted_img = processor.lab_color_transfer(reference_img, target_img, skin_mask, 0.5)
    
    # 创建三联图：原图 | 调整后 | 参考图
    height = target_img.shape[0]
    width = target_img.shape[1]
    
    # 创建画布
    canvas = np.zeros((height, width * 3, 3), dtype=np.uint8)
    
    # 放置图像
    canvas[:, 0:width] = target_img
    canvas[:, width:width*2] = adjusted_img
    canvas[:, width*2:width*3] = reference_img
    
    # 添加标签
    font = cv2.FONT_HERSHEY_SIMPLEX
    font_scale = 1
    color = (255, 255, 255)
    thickness = 2
    
    cv2.putText(canvas, "Original", (10, 30), font, font_scale, color, thickness)
    cv2.putText(canvas, "Adjusted", (width + 10, 30), font, font_scale, color, thickness)
    cv2.putText(canvas, "Reference", (width*2 + 10, 30), font, font_scale, color, thickness)
    
    # 保存对比图
    comparison_path = "comparison_results/side_by_side_comparison.jpg"
    cv2.imwrite(comparison_path, canvas)
    
    print(f"✅ 并排对比图保存: {comparison_path}")
    return True

def interactive_strength_test():
    """交互式强度测试"""
    print("\n🎮 交互式强度测试")
    print("=" * 30)
    
    demo_dir = "demo_images"
    target_path = os.path.join(demo_dir, "target.jpg")
    reference_path = os.path.join(demo_dir, "reference.jpg")
    
    if not os.path.exists(target_path) or not os.path.exists(reference_path):
        print("❌ 缺少演示图片")
        return False
    
    processor = SkinColorTransfer()
    
    while True:
        try:
            strength_input = input("\n请输入调整强度 (0.0-1.0) 或 'q' 退出: ").strip()
            
            if strength_input.lower() == 'q':
                break
            
            strength = float(strength_input)
            
            if not 0.0 <= strength <= 1.0:
                print("❌ 强度必须在 0.0-1.0 之间")
                continue
            
            print(f"🎨 生成强度 {strength:.2f} 的结果...")
            
            # 处理图像
            target_img = cv2.imread(target_path)
            reference_img = cv2.imread(reference_path)
            skin_mask = processor.combine_skin_masks(target_img)
            result_img = processor.lab_color_transfer(reference_img, target_img, skin_mask, strength)
            
            # 保存结果
            output_dir = "interactive_results"
            os.makedirs(output_dir, exist_ok=True)
            output_path = os.path.join(output_dir, f"strength_{strength:.2f}.jpg")
            cv2.imwrite(output_path, result_img)
            
            print(f"✅ 结果保存: {output_path}")
            
        except ValueError:
            print("❌ 请输入有效的数字")
        except KeyboardInterrupt:
            print("\n👋 退出")
            break

def main():
    """主函数"""
    print("🎨 改进的肤色调整演示工具")
    print("=" * 50)
    
    while True:
        print("\n请选择:")
        print("1. 多强度对比演示")
        print("2. 并排对比图")
        print("3. 交互式强度测试")
        print("4. 查看算法改进说明")
        print("5. 退出")
        
        choice = input("\n请输入选择 (1-5): ").strip()
        
        if choice == "1":
            create_comparison_demo()
        elif choice == "2":
            create_side_by_side_comparison()
        elif choice == "3":
            interactive_strength_test()
        elif choice == "4":
            show_improvements()
        elif choice == "5":
            print("👋 再见!")
            break
        else:
            print("❌ 无效选择，请重试")

def show_improvements():
    """显示算法改进说明"""
    print("\n📖 算法改进说明")
    print("=" * 50)
    print("🔧 主要改进:")
    print("1. ❌ 原问题: 完全替换肤色 → 看起来像换脸")
    print("2. ✅ 新方法: 温和调整肤色 → 保持原有特征")
    print()
    print("🎯 改进细节:")
    print("• 不再使用标准差匹配（避免完全替换）")
    print("• 只调整色彩均值差异的一部分")
    print("• L通道（亮度）调整更保守（30%）")
    print("• A/B通道（色彩）调整稍积极（70%）")
    print("• 添加可调节的强度参数")
    print()
    print("💡 使用建议:")
    print("• 强度 0.0: 无变化（原图）")
    print("• 强度 0.3: 轻微调整，自然效果")
    print("• 强度 0.5: 中等调整，平衡效果")
    print("• 强度 0.8: 较强调整，明显效果")
    print("• 强度 1.0: 最强调整")

if __name__ == "__main__":
    # 检查依赖
    try:
        import cv2
        import numpy as np
    except ImportError as e:
        print(f"❌ 缺少依赖: {e}")
        print("💡 请安装: pip install opencv-python numpy")
        exit(1)
    
    main()
