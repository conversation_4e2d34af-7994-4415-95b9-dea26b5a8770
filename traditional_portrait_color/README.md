# 传统算法人像追色工具

基于LAB色彩空间和肤色检测的专业人像色彩匹配工具，无需下载模型，立即可用。

## 🎯 核心算法

**LAB色彩空间 + 肤色检测 + 分区匹配**

1. **智能肤色检测**: 自动识别人像肤色区域
2. **LAB统计匹配**: 在LAB色彩空间进行精确匹配
3. **分区处理**: 只调整肤色，保持背景和衣服不变
4. **边缘羽化**: 自然过渡，无明显边界

## 🚀 特点

- ✅ **立即可用**: 无需下载模型，纯算法实现
- ✅ **专门优化**: 针对人像肤色特别优化
- ✅ **保持背景**: 不影响背景、衣服、头发等
- ✅ **自然效果**: 避免整体色偏，效果自然
- ✅ **快速处理**: 0.1-0.5秒/张，支持批量
- ✅ **双格式输出**: PNG透明 + JPG白底

## 📋 使用方法

### 安装依赖
```bash
pip install opencv-python pillow numpy scikit-image
```

### 单张处理
```bash
python3 skin_color_transfer.py --target 目标.jpg --reference 参考.jpg
```

### 批量处理
```bash
python3 batch_skin_transfer.py --reference 参考.jpg --folder 目标文件夹/
```

### 交互式演示
```bash
python3 demo_skin_transfer.py
```

## 📁 输出结果

```
原图: target.jpg
参考: reference.jpg
结果:
├── target_skin_matched.png (透明背景)
└── target_skin_matched.jpg (白色背景)
```

## 🔧 算法参数

- **肤色检测阈值**: 可调整肤色识别敏感度
- **羽化半径**: 控制边缘过渡自然度
- **匹配强度**: 控制色彩调整幅度
- **亮度保持**: 是否保持原始亮度

## 📊 效果对比

| 特性 | 全图匹配 | 本工具 |
|------|----------|--------|
| 肤色自然度 | 6/10 | 9/10 |
| 背景保持 | 4/10 | 9/10 |
| 处理速度 | 很快 | 快 |
| 无色偏风险 | 高 | 低 |

## 💡 适用场景

- 人像摄影后期统一肤色
- 证件照色彩标准化
- 人像合成色彩匹配
- 批量人像处理
