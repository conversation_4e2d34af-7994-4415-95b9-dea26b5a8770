#!/usr/bin/env python3
"""
安装传统算法人像追色工具依赖
"""

import subprocess
import sys

def install_package(package):
    """安装单个包"""
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        return True
    except subprocess.CalledProcessError:
        return False

def install_dependencies():
    """安装所有依赖"""
    print("🔧 安装传统算法人像追色工具依赖...")
    print("=" * 50)
    
    # 依赖包列表
    packages = [
        "opencv-python",
        "pillow", 
        "numpy",
        "scikit-image",
        "argparse"
    ]
    
    failed_packages = []
    
    for package in packages:
        print(f"📦 安装 {package}...")
        if install_package(package):
            print(f"✅ {package} 安装成功")
        else:
            print(f"❌ {package} 安装失败")
            failed_packages.append(package)
    
    print("=" * 50)
    
    if failed_packages:
        print(f"❌ 以下包安装失败: {', '.join(failed_packages)}")
        print("💡 请手动安装:")
        for pkg in failed_packages:
            print(f"   pip install {pkg}")
        return False
    else:
        print("🎉 所有依赖安装成功!")
        print("\n📖 使用方法:")
        print("   python3 demo_skin_transfer.py  # 交互式演示")
        print("   python3 skin_color_transfer.py --target 目标.jpg --reference 参考.jpg")
        return True

def test_installation():
    """测试安装是否成功"""
    print("\n🧪 测试安装...")
    
    try:
        import cv2
        import numpy as np
        from PIL import Image
        from skimage import color
        
        print("✅ 所有模块导入成功")
        print(f"📦 OpenCV版本: {cv2.__version__}")
        print(f"📦 NumPy版本: {np.__version__}")
        
        return True
        
    except ImportError as e:
        print(f"❌ 模块导入失败: {e}")
        return False

if __name__ == "__main__":
    success = install_dependencies()
    
    if success:
        test_installation()
        print("\n🚀 安装完成! 可以开始使用了:")
        print("   python3 demo_skin_transfer.py")
    else:
        print("\n💡 如果安装遇到问题，请尝试:")
        print("   1. 更新pip: python3 -m pip install --upgrade pip")
        print("   2. 使用清华源: pip install -i https://pypi.tuna.tsinghua.edu.cn/simple opencv-python")
        print("   3. 检查网络连接")
