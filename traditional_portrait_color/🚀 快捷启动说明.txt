🎨 人像追色工具 - 快捷启动说明
========================================

📁 文件说明：
├── 简单启动.py                    ⭐ 推荐：双击即可运行
├── 启动人像追色工具.command        macOS专用启动脚本
├── 启动人像追色工具.bat            Windows专用启动脚本
└── demo_skin_transfer.py          手动启动（需要终端）

🚀 启动方式：

【方式1 - 最简单】双击运行
  📱 双击 "简单启动.py"
  ✅ 自动检查依赖
  ✅ 自动安装缺失包
  ✅ 启动交互界面

【方式2 - macOS用户】
  📱 双击 "启动人像追色工具.command"
  ✅ 终端窗口启动
  ✅ 自动处理依赖

【方式3 - Windows用户】
  📱 双击 "启动人像追色工具.bat"
  ✅ 命令行窗口启动
  ✅ 自动处理依赖

【方式4 - 手动启动】
  💻 打开终端，进入此文件夹
  💻 运行：python3 demo_skin_transfer.py

💡 使用步骤：

1️⃣ 准备图片
   - 参考图片：理想肤色的人像照片
   - 目标图片：需要调整肤色的人像照片

2️⃣ 启动工具
   - 双击 "简单启动.py"
   - 选择功能菜单

3️⃣ 选择功能
   - 1. 肤色检测测试（查看肤色识别效果）
   - 2. 完整色彩匹配演示
   - 3. 自定义图片路径
   - 4. 批量处理演示

4️⃣ 查看结果
   - 结果自动保存到对应文件夹
   - PNG版本：透明背景
   - JPG版本：白色背景

🔧 如果遇到问题：

❌ Python未安装
   💡 下载安装：https://www.python.org/downloads/
   💡 安装时勾选"Add Python to PATH"

❌ 依赖包安装失败
   💻 手动安装：pip install opencv-python pillow numpy scikit-image

❌ 双击.py文件无法运行
   💻 右键 → 打开方式 → Python
   💻 或在终端运行：python3 简单启动.py

📞 技术支持：
   - 算法：LAB色彩空间 + 肤色检测
   - 特点：只调肤色，保持背景不变
   - 速度：0.1-0.5秒/张
   - 效果：专业级人像色彩匹配