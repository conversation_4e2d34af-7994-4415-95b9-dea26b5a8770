#!/usr/bin/env python3
"""
批量肤色色彩匹配工具
"""

import os
import sys
import argparse
import glob
from pathlib import Path
from skin_color_transfer import SkinColorTransfer

def batch_skin_transfer(reference_path, input_folder, output_folder="batch_skin_results"):
    """批量处理文件夹中的图片"""
    
    # 检查参考图片
    if not os.path.exists(reference_path):
        print(f"❌ 参考图片不存在: {reference_path}")
        return False
    
    # 检查输入文件夹
    if not os.path.exists(input_folder):
        print(f"❌ 输入文件夹不存在: {input_folder}")
        return False
    
    # 创建输出文件夹
    os.makedirs(output_folder, exist_ok=True)
    
    # 支持的图片格式
    supported_formats = ['*.jpg', '*.jpeg', '*.png', '*.bmp', '*.tiff', '*.webp',
                        '*.JPG', '*.JPEG', '*.PNG', '*.BMP', '*.TIFF', '*.WEBP']
    
    # 获取所有图片文件
    image_files = []
    for pattern in supported_formats:
        image_files.extend(glob.glob(os.path.join(input_folder, pattern)))
    
    if not image_files:
        print(f"❌ 在文件夹中未找到图片文件: {input_folder}")
        return False
    
    print(f"📁 找到 {len(image_files)} 张图片")
    print(f"🎯 参考图片: {os.path.basename(reference_path)}")
    print(f"📂 输出文件夹: {output_folder}")
    print("=" * 60)
    
    # 创建处理器
    processor = SkinColorTransfer()
    
    # 批量处理
    success_count = 0
    failed_count = 0
    
    for i, image_file in enumerate(image_files, 1):
        print(f"\n[{i}/{len(image_files)}] 处理: {os.path.basename(image_file)}")
        
        try:
            # 为每张图片创建子文件夹
            base_name = Path(image_file).stem
            image_output_dir = os.path.join(output_folder, base_name)
            
            success = processor.transfer_skin_color(image_file, reference_path, image_output_dir)
            
            if success:
                success_count += 1
                print(f"  ✅ 成功")
            else:
                failed_count += 1
                print(f"  ❌ 失败")
                
        except Exception as e:
            failed_count += 1
            print(f"  💥 异常: {e}")
    
    print("\n" + "=" * 60)
    print(f"🎉 批量处理完成!")
    print(f"✅ 成功: {success_count} 张")
    print(f"❌ 失败: {failed_count} 张")
    print(f"📁 结果保存在: {output_folder}")
    
    return success_count > 0

def main():
    parser = argparse.ArgumentParser(description='批量肤色色彩匹配工具')
    parser.add_argument('--reference', '-r', required=True, help='参考图片路径')
    parser.add_argument('--folder', '-f', required=True, help='输入文件夹路径')
    parser.add_argument('--output', '-o', default='batch_skin_results', help='输出文件夹')
    
    args = parser.parse_args()
    
    # 执行批量处理
    batch_skin_transfer(args.reference, args.folder, args.output)

if __name__ == "__main__":
    main()
