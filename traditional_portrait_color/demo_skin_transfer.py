#!/usr/bin/env python3
"""
传统算法人像追色演示工具
"""

import os
import sys
import cv2
import numpy as np
from skin_color_transfer import SkinColorTransfer

def create_demo_images():
    """创建演示用的测试图片目录"""
    print("📸 准备演示图片...")
    
    demo_dir = "demo_images"
    os.makedirs(demo_dir, exist_ok=True)
    
    print(f"📁 请将测试图片放入: {demo_dir}/")
    print("   - reference.jpg (参考图片 - 理想肤色)")
    print("   - target.jpg (目标图片 - 需要调整肤色)")
    print("   - 或者拖拽任意人像照片到该文件夹")
    
    return demo_dir

def test_skin_detection():
    """测试肤色检测功能"""
    print("\n🔍 肤色检测测试")
    print("=" * 40)
    
    demo_dir = create_demo_images()
    test_image = os.path.join(demo_dir, "target.jpg")
    
    if not os.path.exists(test_image):
        print(f"❌ 请准备测试图片: {test_image}")
        return False
    
    # 创建处理器
    processor = SkinColorTransfer()
    
    # 读取图像
    image = cv2.imread(test_image)
    if image is None:
        print(f"❌ 无法读取图片: {test_image}")
        return False
    
    print(f"✅ 读取图片: {test_image}")
    print(f"📏 图片尺寸: {image.shape[1]}x{image.shape[0]}")
    
    # 检测肤色
    print("🔍 检测肤色区域...")
    skin_mask = processor.combine_skin_masks(image)
    
    # 计算肤色比例
    skin_ratio = np.sum(skin_mask > 0) / (skin_mask.shape[0] * skin_mask.shape[1])
    print(f"📊 肤色区域占比: {skin_ratio:.2%}")
    
    # 保存肤色掩码
    mask_output = "skin_detection_test.jpg"
    cv2.imwrite(mask_output, skin_mask)
    print(f"🔍 肤色掩码保存为: {mask_output}")
    
    # 创建可视化结果
    visualization = image.copy()
    visualization[skin_mask > 0] = [0, 255, 0]  # 绿色标记肤色区域
    vis_output = "skin_detection_visualization.jpg"
    cv2.imwrite(vis_output, visualization)
    print(f"👁️ 可视化结果: {vis_output}")
    
    if skin_ratio > 0.05:
        print("✅ 肤色检测成功!")
        return True
    else:
        print("⚠️ 检测到的肤色区域较小，可能需要调整参数")
        return False

def run_demo():
    """运行完整演示"""
    print("\n🎨 肤色色彩匹配演示")
    print("=" * 40)
    
    demo_dir = create_demo_images()
    
    # 检查演示图片
    reference_path = os.path.join(demo_dir, "reference.jpg")
    target_path = os.path.join(demo_dir, "target.jpg")
    
    if not os.path.exists(reference_path):
        print(f"❌ 请准备参考图片: {reference_path}")
        return False
    
    if not os.path.exists(target_path):
        print(f"❌ 请准备目标图片: {target_path}")
        return False
    
    print(f"✅ 找到参考图片: {reference_path}")
    print(f"✅ 找到目标图片: {target_path}")
    
    # 创建处理器
    print("\n🔧 初始化处理器...")
    processor = SkinColorTransfer()
    
    # 执行处理
    print("\n🎯 开始处理...")
    output_dir = "demo_results"
    success = processor.transfer_skin_color(target_path, reference_path, output_dir)
    
    if success:
        print("\n🎉 演示完成!")
        print(f"📁 结果保存在: {output_dir}/")
        print("💡 对比原图和结果图片查看效果")
        
        # 尝试打开结果文件夹
        try:
            if sys.platform == "darwin":  # macOS
                os.system(f"open {output_dir}")
            elif sys.platform == "win32":  # Windows
                os.system(f"start {output_dir}")
            else:  # Linux
                os.system(f"xdg-open {output_dir}")
        except:
            pass
            
        return True
    else:
        print("\n❌ 演示失败")
        return False

def interactive_demo():
    """交互式演示"""
    print("🎮 传统算法人像追色演示")
    print("=" * 40)
    
    while True:
        print("\n请选择:")
        print("1. 肤色检测测试")
        print("2. 完整色彩匹配演示")
        print("3. 自定义图片路径")
        print("4. 批量处理演示")
        print("5. 算法参数说明")
        print("6. 退出")
        
        choice = input("\n请输入选择 (1-6): ").strip()
        
        if choice == "1":
            test_skin_detection()
        elif choice == "2":
            run_demo()
        elif choice == "3":
            custom_demo()
        elif choice == "4":
            batch_demo()
        elif choice == "5":
            show_algorithm_info()
        elif choice == "6":
            print("👋 再见!")
            break
        else:
            print("❌ 无效选择，请重试")

def custom_demo():
    """自定义路径演示"""
    print("\n📝 自定义图片路径")
    
    reference = input("请输入参考图片路径: ").strip().strip('"')
    target = input("请输入目标图片路径: ").strip().strip('"')
    
    if not os.path.exists(reference):
        print(f"❌ 参考图片不存在: {reference}")
        return
    
    if not os.path.exists(target):
        print(f"❌ 目标图片不存在: {target}")
        return
    
    # 创建处理器并执行
    processor = SkinColorTransfer()
    output_dir = "custom_skin_results"
    
    success = processor.transfer_skin_color(target, reference, output_dir)
    
    if success:
        print(f"✅ 处理完成! 结果保存在: {output_dir}/")
    else:
        print("❌ 处理失败")

def batch_demo():
    """批量处理演示"""
    print("\n📁 批量处理演示")
    
    reference = input("请输入参考图片路径: ").strip().strip('"')
    folder = input("请输入目标文件夹路径: ").strip().strip('"')
    
    if not os.path.exists(reference):
        print(f"❌ 参考图片不存在: {reference}")
        return
    
    if not os.path.exists(folder):
        print(f"❌ 目标文件夹不存在: {folder}")
        return
    
    # 导入批量处理模块
    from batch_skin_transfer import batch_skin_transfer
    
    output_dir = "batch_demo_results"
    success = batch_skin_transfer(reference, folder, output_dir)
    
    if success:
        print(f"✅ 批量处理完成! 结果保存在: {output_dir}/")
    else:
        print("❌ 批量处理失败")

def show_algorithm_info():
    """显示算法信息"""
    print("\n📖 算法说明")
    print("=" * 50)
    print("🔬 核心算法: LAB色彩空间 + 肤色检测 + 分区匹配")
    print()
    print("📋 处理步骤:")
    print("1. 🔍 肤色检测:")
    print("   - 使用HSV和YCrCb双重检测")
    print("   - 形态学操作去除噪声")
    print("   - 高斯模糊自然边缘")
    print()
    print("2. 🎨 色彩匹配:")
    print("   - 转换到LAB色彩空间")
    print("   - 计算肤色区域统计信息")
    print("   - 只调整肤色，保持背景")
    print()
    print("3. ✨ 优势:")
    print("   - 无需训练模型，立即可用")
    print("   - 专门针对人像优化")
    print("   - 保持背景和衣服不变")
    print("   - 处理速度快")
    print()
    print("4. 📊 参数:")
    print("   - HSV肤色范围: H(0-20), S(20-255), V(70-255)")
    print("   - YCrCb肤色范围: Cr(135-180), Cb(85-135)")
    print("   - 形态学核大小: 11x11椭圆")

if __name__ == "__main__":
    # 检查依赖
    try:
        import cv2
        import numpy as np
        from PIL import Image
        from skimage import color
    except ImportError as e:
        print(f"❌ 缺少依赖: {e}")
        print("💡 请安装: pip install opencv-python pillow numpy scikit-image")
        sys.exit(1)
    
    # 运行交互式演示
    interactive_demo()
