#!/usr/bin/env python3
"""
人像追色演示脚本 - 快速测试效果
"""

import os
import sys
from color_transfer import PortraitColorTransfer

def create_demo_images():
    """创建演示用的测试图片"""
    print("📸 创建演示图片...")
    
    # 这里可以放一些测试图片的创建逻辑
    # 或者提示用户准备测试图片
    
    demo_dir = "demo_images"
    os.makedirs(demo_dir, exist_ok=True)
    
    print(f"📁 请将测试图片放入: {demo_dir}/")
    print("   - reference.jpg (参考图片)")
    print("   - target.jpg (目标图片)")
    
    return demo_dir

def run_demo():
    """运行演示"""
    print("🎨 人像追色工具演示")
    print("=" * 40)
    
    # 创建演示目录
    demo_dir = create_demo_images()
    
    # 检查演示图片
    reference_path = os.path.join(demo_dir, "reference.jpg")
    target_path = os.path.join(demo_dir, "target.jpg")
    
    if not os.path.exists(reference_path):
        print(f"❌ 请准备参考图片: {reference_path}")
        return False
    
    if not os.path.exists(target_path):
        print(f"❌ 请准备目标图片: {target_path}")
        return False
    
    print(f"✅ 找到参考图片: {reference_path}")
    print(f"✅ 找到目标图片: {target_path}")
    
    # 创建处理器
    print("\n🔧 初始化处理器...")
    processor = PortraitColorTransfer()
    
    # 执行处理
    print("\n🎯 开始处理...")
    output_dir = "demo_results"
    success = processor.transfer_color(target_path, reference_path, output_dir)
    
    if success:
        print("\n🎉 演示完成!")
        print(f"📁 结果保存在: {output_dir}/")
        print("💡 对比原图和结果图片查看效果")
        
        # 尝试打开结果文件夹
        try:
            if sys.platform == "darwin":  # macOS
                os.system(f"open {output_dir}")
            elif sys.platform == "win32":  # Windows
                os.system(f"start {output_dir}")
            else:  # Linux
                os.system(f"xdg-open {output_dir}")
        except:
            pass
            
        return True
    else:
        print("\n❌ 演示失败")
        return False

def interactive_demo():
    """交互式演示"""
    print("🎮 交互式人像追色演示")
    print("=" * 40)
    
    while True:
        print("\n请选择:")
        print("1. 运行演示 (需要准备demo_images/reference.jpg和target.jpg)")
        print("2. 自定义图片路径")
        print("3. 批量处理演示")
        print("4. 退出")
        
        choice = input("\n请输入选择 (1-4): ").strip()
        
        if choice == "1":
            run_demo()
        elif choice == "2":
            custom_demo()
        elif choice == "3":
            batch_demo()
        elif choice == "4":
            print("👋 再见!")
            break
        else:
            print("❌ 无效选择，请重试")

def custom_demo():
    """自定义路径演示"""
    print("\n📝 自定义图片路径")
    
    reference = input("请输入参考图片路径: ").strip().strip('"')
    target = input("请输入目标图片路径: ").strip().strip('"')
    
    if not os.path.exists(reference):
        print(f"❌ 参考图片不存在: {reference}")
        return
    
    if not os.path.exists(target):
        print(f"❌ 目标图片不存在: {target}")
        return
    
    # 创建处理器并执行
    processor = PortraitColorTransfer()
    output_dir = "custom_results"
    
    success = processor.transfer_color(target, reference, output_dir)
    
    if success:
        print(f"✅ 处理完成! 结果保存在: {output_dir}/")
    else:
        print("❌ 处理失败")

def batch_demo():
    """批量处理演示"""
    print("\n📁 批量处理演示")
    
    reference = input("请输入参考图片路径: ").strip().strip('"')
    folder = input("请输入目标文件夹路径: ").strip().strip('"')
    
    if not os.path.exists(reference):
        print(f"❌ 参考图片不存在: {reference}")
        return
    
    if not os.path.exists(folder):
        print(f"❌ 目标文件夹不存在: {folder}")
        return
    
    # 导入批量处理模块
    from batch_transfer import batch_process
    
    output_dir = "batch_demo_results"
    success = batch_process(reference, folder, output_dir)
    
    if success:
        print(f"✅ 批量处理完成! 结果保存在: {output_dir}/")
    else:
        print("❌ 批量处理失败")

if __name__ == "__main__":
    # 检查依赖
    try:
        import torch
        import cv2
        import PIL
        import face_recognition
    except ImportError as e:
        print(f"❌ 缺少依赖: {e}")
        print("💡 请先运行: python3 install_dependencies.py")
        sys.exit(1)
    
    # 运行交互式演示
    interactive_demo()
