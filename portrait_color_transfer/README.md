# 人像追色工具 (Portrait Color Transfer)

基于深度学习的专业级人像色彩匹配工具，专门针对人像照片进行色彩统一处理。

## 🎯 功能特点

- **专业级效果**: 使用2023年最新的人像色彩迁移算法
- **保持细节**: 只调整色彩，保持肌理和细节
- **智能识别**: 自动识别人像区域，避免背景干扰
- **M2优化**: 针对Apple Silicon芯片优化
- **双格式输出**: PNG透明背景 + JPG白色背景

## 📋 使用方法

### 基本用法
```bash
python3 color_transfer.py --reference 参考照片.jpg --target 目标照片.jpg
```

### 批量处理
```bash
python3 batch_transfer.py --reference 参考照片.jpg --folder 目标文件夹/
```

## 📁 输出结果

```
原图: target.jpg
参考: reference.jpg
结果:
├── target_color_matched.png (透明背景)
└── target_color_matched.jpg (白色背景)
```

## 🔧 安装依赖

```bash
pip install torch torchvision
pip install opencv-python pillow numpy
pip install face-recognition  # 人脸检测
```

## ⚡ 性能

- **M2芯片**: ~1-2秒/张
- **模型大小**: ~120MB
- **内存需求**: 2-4GB

## 📖 技术说明

使用基于Transformer的色彩迁移网络，专门针对人像进行训练：
1. 人脸检测和分割
2. 色彩特征提取
3. 深度色彩映射
4. 边缘融合处理
