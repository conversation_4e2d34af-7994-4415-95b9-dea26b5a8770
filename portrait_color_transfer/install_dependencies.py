#!/usr/bin/env python3
"""
安装依赖包脚本
"""

import subprocess
import sys
import platform

def install_package(package):
    """安装单个包"""
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        return True
    except subprocess.CalledProcessError:
        return False

def check_pytorch():
    """检查并安装PyTorch"""
    try:
        import torch
        print(f"✅ PyTorch已安装: {torch.__version__}")
        
        # 检查MPS支持 (Apple Silicon)
        if torch.backends.mps.is_available():
            print("✅ MPS (Apple Silicon GPU) 支持可用")
        elif torch.cuda.is_available():
            print("✅ CUDA支持可用")
        else:
            print("ℹ️ 将使用CPU模式")
            
        return True
    except ImportError:
        print("📦 正在安装PyTorch...")
        
        # 根据系统选择安装命令
        system = platform.system()
        machine = platform.machine()
        
        if system == "Darwin" and machine == "arm64":  # Apple Silicon
            success = install_package("torch torchvision --index-url https://download.pytorch.org/whl/cpu")
        else:
            success = install_package("torch torchvision")
            
        if success:
            print("✅ PyTorch安装成功")
            return True
        else:
            print("❌ PyTorch安装失败")
            return False

def install_dependencies():
    """安装所有依赖"""
    print("🔧 开始安装人像追色工具依赖包...")
    print("=" * 50)
    
    # 基础包列表
    packages = [
        "opencv-python",
        "pillow", 
        "numpy",
        "face-recognition",
        "argparse"
    ]
    
    # 检查并安装PyTorch
    if not check_pytorch():
        print("❌ PyTorch安装失败，请手动安装")
        return False
    
    # 安装其他包
    failed_packages = []
    
    for package in packages:
        print(f"📦 安装 {package}...")
        if install_package(package):
            print(f"✅ {package} 安装成功")
        else:
            print(f"❌ {package} 安装失败")
            failed_packages.append(package)
    
    print("=" * 50)
    
    if failed_packages:
        print(f"❌ 以下包安装失败: {', '.join(failed_packages)}")
        print("💡 请手动安装:")
        for pkg in failed_packages:
            print(f"   pip install {pkg}")
        return False
    else:
        print("🎉 所有依赖安装成功!")
        print("\n📖 使用方法:")
        print("   python3 color_transfer.py --target 目标.jpg --reference 参考.jpg")
        print("   python3 batch_transfer.py --reference 参考.jpg --folder 文件夹/")
        return True

def test_installation():
    """测试安装是否成功"""
    print("\n🧪 测试安装...")
    
    try:
        import torch
        import cv2
        import PIL
        import numpy
        import face_recognition
        
        print("✅ 所有模块导入成功")
        
        # 测试设备
        if torch.backends.mps.is_available():
            device = "MPS (Apple Silicon)"
        elif torch.cuda.is_available():
            device = "CUDA"
        else:
            device = "CPU"
            
        print(f"🖥️ 将使用设备: {device}")
        return True
        
    except ImportError as e:
        print(f"❌ 模块导入失败: {e}")
        return False

if __name__ == "__main__":
    success = install_dependencies()
    
    if success:
        test_installation()
    else:
        print("\n💡 如果安装遇到问题，请尝试:")
        print("   1. 更新pip: python3 -m pip install --upgrade pip")
        print("   2. 使用conda: conda install pytorch torchvision")
        print("   3. 检查网络连接")
