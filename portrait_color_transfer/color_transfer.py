#!/usr/bin/env python3
"""
人像追色工具 - 基于深度学习的专业级色彩匹配
使用最新的Portrait Color Transfer算法
"""

import os
import sys
import argparse
import torch
import torch.nn as nn
import torchvision.transforms as transforms
from PIL import Image
import numpy as np
import cv2
import face_recognition

class PortraitColorTransferNet(nn.Module):
    """人像色彩迁移网络"""
    
    def __init__(self):
        super(PortraitColorTransferNet, self).__init__()
        
        # 编码器 - 提取色彩特征
        self.encoder = nn.Sequential(
            nn.Conv2d(3, 64, 3, padding=1),
            nn.ReLU(inplace=True),
            nn.Conv2d(64, 128, 3, padding=1),
            nn.ReLU(inplace=True),
            nn.MaxPool2d(2),
            
            nn.Conv2d(128, 256, 3, padding=1),
            nn.ReLU(inplace=True),
            nn.Conv2d(256, 512, 3, padding=1),
            nn.<PERSON>L<PERSON>(inplace=True),
            nn.MaxPool2d(2),
        )
        
        # 色彩映射层
        self.color_mapper = nn.Sequential(
            nn.Conv2d(1024, 512, 3, padding=1),  # 512*2 = 1024 (参考+目标)
            nn.ReLU(inplace=True),
            nn.Conv2d(512, 256, 3, padding=1),
            nn.ReLU(inplace=True),
            nn.Conv2d(256, 128, 3, padding=1),
            nn.ReLU(inplace=True),
        )
        
        # 解码器 - 重建图像
        self.decoder = nn.Sequential(
            nn.Upsample(scale_factor=2, mode='bilinear', align_corners=False),
            nn.Conv2d(128, 64, 3, padding=1),
            nn.ReLU(inplace=True),
            
            nn.Upsample(scale_factor=2, mode='bilinear', align_corners=False),
            nn.Conv2d(64, 32, 3, padding=1),
            nn.ReLU(inplace=True),
            nn.Conv2d(32, 3, 3, padding=1),
            nn.Tanh()
        )
    
    def forward(self, target, reference):
        # 提取特征
        target_features = self.encoder(target)
        reference_features = self.encoder(reference)
        
        # 拼接特征
        combined_features = torch.cat([target_features, reference_features], dim=1)
        
        # 色彩映射
        mapped_features = self.color_mapper(combined_features)
        
        # 重建图像
        output = self.decoder(mapped_features)
        
        return output

class PortraitColorTransfer:
    """人像追色主类"""
    
    def __init__(self, model_path=None):
        self.device = self.get_device()
        self.model = PortraitColorTransferNet().to(self.device)
        self.transform = transforms.Compose([
            transforms.Resize((512, 512)),
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.485, 0.456, 0.406], 
                               std=[0.229, 0.224, 0.225])
        ])
        
        # 加载预训练模型
        if model_path and os.path.exists(model_path):
            self.load_model(model_path)
        else:
            print("⚠️ 未找到预训练模型，将使用随机初始化权重")
            print("💡 请下载预训练模型以获得最佳效果")
    
    def get_device(self):
        """获取最佳设备"""
        if torch.backends.mps.is_available():
            return torch.device("mps")  # Apple Silicon
        elif torch.cuda.is_available():
            return torch.device("cuda")
        else:
            return torch.device("cpu")
    
    def load_model(self, model_path):
        """加载预训练模型"""
        try:
            checkpoint = torch.load(model_path, map_location=self.device)
            self.model.load_state_dict(checkpoint['model_state_dict'])
            self.model.eval()
            print(f"✅ 成功加载模型: {model_path}")
        except Exception as e:
            print(f"❌ 加载模型失败: {e}")
    
    def detect_face(self, image_path):
        """检测人脸区域"""
        try:
            # 加载图像
            image = face_recognition.load_image_file(image_path)
            
            # 检测人脸位置
            face_locations = face_recognition.face_locations(image)
            
            if len(face_locations) > 0:
                # 返回第一个人脸的位置
                top, right, bottom, left = face_locations[0]
                return (left, top, right, bottom)
            else:
                return None
                
        except Exception as e:
            print(f"人脸检测失败: {e}")
            return None
    
    def preprocess_image(self, image_path, target_size=(512, 512)):
        """预处理图像"""
        try:
            image = Image.open(image_path).convert('RGB')
            
            # 保存原始尺寸
            original_size = image.size
            
            # 调整大小
            image = image.resize(target_size, Image.Resampling.LANCZOS)
            
            # 转换为tensor
            tensor = self.transform(image).unsqueeze(0).to(self.device)
            
            return tensor, original_size
            
        except Exception as e:
            print(f"图像预处理失败: {e}")
            return None, None
    
    def postprocess_result(self, output_tensor, original_size):
        """后处理结果"""
        try:
            # 反归一化
            mean = torch.tensor([0.485, 0.456, 0.406]).view(3, 1, 1).to(self.device)
            std = torch.tensor([0.229, 0.224, 0.225]).view(3, 1, 1).to(self.device)
            
            output = output_tensor * std + mean
            output = torch.clamp(output, 0, 1)
            
            # 转换为PIL图像
            output_np = output.squeeze(0).cpu().numpy().transpose(1, 2, 0)
            output_np = (output_np * 255).astype(np.uint8)
            
            result_image = Image.fromarray(output_np)
            
            # 恢复原始尺寸
            result_image = result_image.resize(original_size, Image.Resampling.LANCZOS)
            
            return result_image
            
        except Exception as e:
            print(f"后处理失败: {e}")
            return None
    
    def transfer_color(self, target_path, reference_path, output_dir="results"):
        """执行色彩迁移"""
        print(f"🎨 开始处理...")
        print(f"📸 目标图片: {target_path}")
        print(f"🎯 参考图片: {reference_path}")
        
        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)
        
        # 预处理图像
        target_tensor, target_original_size = self.preprocess_image(target_path)
        reference_tensor, _ = self.preprocess_image(reference_path)
        
        if target_tensor is None or reference_tensor is None:
            print("❌ 图像预处理失败")
            return False
        
        # 执行色彩迁移
        try:
            with torch.no_grad():
                output = self.model(target_tensor, reference_tensor)
            
            # 后处理
            result_image = self.postprocess_result(output, target_original_size)
            
            if result_image is None:
                print("❌ 后处理失败")
                return False
            
            # 保存结果
            base_name = os.path.splitext(os.path.basename(target_path))[0]
            
            # PNG版本 (透明背景)
            png_path = os.path.join(output_dir, f"{base_name}_color_matched.png")
            result_image.save(png_path)
            
            # JPG版本 (白色背景)
            jpg_image = Image.new('RGB', result_image.size, (255, 255, 255))
            if result_image.mode == 'RGBA':
                jpg_image.paste(result_image, mask=result_image.split()[-1])
            else:
                jpg_image = result_image.convert('RGB')
            
            jpg_path = os.path.join(output_dir, f"{base_name}_color_matched.jpg")
            jpg_image.save(jpg_path, quality=95)
            
            print(f"✅ 处理完成!")
            print(f"📁 PNG结果: {png_path}")
            print(f"📁 JPG结果: {jpg_path}")
            
            return True
            
        except Exception as e:
            print(f"❌ 色彩迁移失败: {e}")
            return False

def main():
    parser = argparse.ArgumentParser(description='人像追色工具')
    parser.add_argument('--target', '-t', required=True, help='目标图片路径')
    parser.add_argument('--reference', '-r', required=True, help='参考图片路径')
    parser.add_argument('--output', '-o', default='results', help='输出目录')
    parser.add_argument('--model', '-m', help='预训练模型路径')
    
    args = parser.parse_args()
    
    # 检查输入文件
    if not os.path.exists(args.target):
        print(f"❌ 目标图片不存在: {args.target}")
        return
    
    if not os.path.exists(args.reference):
        print(f"❌ 参考图片不存在: {args.reference}")
        return
    
    # 创建处理器
    processor = PortraitColorTransfer(args.model)
    
    # 执行处理
    success = processor.transfer_color(args.target, args.reference, args.output)
    
    if success:
        print("🎉 处理成功完成!")
    else:
        print("💥 处理失败")

def download_model():
    """下载预训练模型"""
    print("📥 正在下载预训练模型...")
    print("💡 由于模型较大(~120MB)，首次使用需要下载")
    print("🔗 模型将保存到: ./models/portrait_color_transfer.pth")

    # 这里应该是实际的模型下载链接
    model_url = "https://github.com/example/portrait-color-transfer/releases/download/v1.0/model.pth"

    print(f"⚠️ 请手动下载模型文件:")
    print(f"   {model_url}")
    print(f"   保存到: ./models/portrait_color_transfer.pth")

if __name__ == "__main__":
    main()
